pub mod health;
pub mod auth;
pub mod mfa;
pub mod session;
pub mod password_reset;
pub mod rbac;
pub mod email_verification;
pub mod oauth;
pub mod tenant_handlers;
pub mod service_client_handlers;

use actix_web::web;

pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/auth")
            .configure(auth::configure_auth_routes)
            .configure(oauth::configure_oauth_routes)
    )
    .service(
        web::scope("/api/v1")
            .configure(mfa::configure_routes)
            .configure(password_reset::configure_routes)
            .configure(rbac::configure_routes)
            .configure(session::configure_routes)
            .configure(email_verification::configure_email_verification_routes)
            .configure(tenant_handlers::configure_tenant_routes)
            .configure(service_client_handlers::configure_client_routes)
    );
}
