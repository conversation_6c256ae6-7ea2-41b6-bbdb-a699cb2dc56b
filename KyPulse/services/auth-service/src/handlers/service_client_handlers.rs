use actix_web::{web, HttpRequest, HttpResponse, Result};
use uuid::Uuid;
use crate::models::{
    CreateServiceClientRequest, UpdateServiceClientRequest, ClientListRequest
};
use crate::services::ServiceClientService;
use crate::utils::errors::AppError;
use crate::middleware::extract_user_id;
use tracing::{info, warn, error, instrument};
use serde_json::json;

#[instrument(skip(client_service))]
pub async fn create_client(
    req: HttpRequest,
    client_service: web::Data<ServiceClientService>,
    request: web::Json<CreateServiceClientRequest>,
) -> Result<HttpResponse, AppError> {
    let created_by = extract_user_id(&req);
    
    info!(
        client_name = %request.client_name,
        client_type = ?request.client_type,
        tenant_id = %request.tenant_id,
        created_by = ?created_by,
        "Creating new service client"
    );

    let client_with_secret = client_service
        .create_client(request.into_inner(), created_by)
        .await?;

    Ok(HttpResponse::Created().json(json!({
        "success": true,
        "message": "Service client created successfully",
        "data": client_with_secret,
        "warning": "Store the client_secret securely. It will not be shown again."
    })))
}

#[instrument(skip(client_service))]
pub async fn get_client(
    path: web::Path<String>,
    client_service: web::Data<ServiceClientService>,
) -> Result<HttpResponse, AppError> {
    let client_id = path.into_inner();
    
    let client = client_service.get_client(&client_id).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": client
    })))
}

#[instrument(skip(client_service))]
pub async fn get_client_by_id(
    path: web::Path<Uuid>,
    client_service: web::Data<ServiceClientService>,
) -> Result<HttpResponse, AppError> {
    let client_id = path.into_inner();
    
    let client = client_service.get_client_by_id(client_id).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": client
    })))
}

#[instrument(skip(client_service))]
pub async fn update_client(
    req: HttpRequest,
    path: web::Path<String>,
    client_service: web::Data<ServiceClientService>,
    request: web::Json<UpdateServiceClientRequest>,
) -> Result<HttpResponse, AppError> {
    let client_id = path.into_inner();
    let updated_by = extract_user_id(&req);
    
    info!(
        client_id = %client_id,
        updated_by = ?updated_by,
        "Updating service client"
    );

    let client = client_service
        .update_client(&client_id, request.into_inner(), updated_by)
        .await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "Service client updated successfully",
        "data": client
    })))
}

#[instrument(skip(client_service))]
pub async fn delete_client(
    path: web::Path<String>,
    client_service: web::Data<ServiceClientService>,
) -> Result<HttpResponse, AppError> {
    let client_id = path.into_inner();
    
    info!(client_id = %client_id, "Deleting service client");

    client_service.delete_client(&client_id).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "Service client deleted successfully"
    })))
}

#[instrument(skip(client_service))]
pub async fn list_clients(
    query: web::Query<ClientListRequest>,
    client_service: web::Data<ServiceClientService>,
) -> Result<HttpResponse, AppError> {
    let request = query.into_inner();
    
    let response = client_service.list_clients(request).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": response
    })))
}

#[instrument(skip(client_service))]
pub async fn regenerate_client_secret(
    req: HttpRequest,
    path: web::Path<String>,
    client_service: web::Data<ServiceClientService>,
) -> Result<HttpResponse, AppError> {
    let client_id = path.into_inner();
    let updated_by = extract_user_id(&req);
    
    info!(
        client_id = %client_id,
        updated_by = ?updated_by,
        "Regenerating client secret"
    );

    let new_secret = client_service
        .regenerate_client_secret(&client_id, updated_by)
        .await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "Client secret regenerated successfully",
        "data": {
            "client_id": client_id,
            "client_secret": new_secret
        },
        "warning": "Store the new client_secret securely. It will not be shown again."
    })))
}

#[instrument(skip(client_service))]
pub async fn verify_client_credentials(
    client_service: web::Data<ServiceClientService>,
    request: web::Json<serde_json::Value>,
) -> Result<HttpResponse, AppError> {
    let client_id = request.get("client_id")
        .and_then(|v| v.as_str())
        .ok_or_else(|| AppError::BadRequest("client_id is required".to_string()))?;

    let client_secret = request.get("client_secret")
        .and_then(|v| v.as_str())
        .ok_or_else(|| AppError::BadRequest("client_secret is required".to_string()))?;

    let client_context = client_service
        .verify_client_credentials(client_id, client_secret)
        .await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "Client credentials verified successfully",
        "data": {
            "client_id": client_context.client_id,
            "client_name": client_context.client_name,
            "client_type": client_context.client_type,
            "tenant_id": client_context.tenant_id,
            "features": client_context.features,
            "rate_limits": client_context.rate_limits
        }
    })))
}

// Tenant-specific client endpoints
#[instrument(skip(client_service))]
pub async fn list_tenant_clients(
    path: web::Path<Uuid>,
    query: web::Query<ClientListRequest>,
    client_service: web::Data<ServiceClientService>,
) -> Result<HttpResponse, AppError> {
    let tenant_id = path.into_inner();
    let mut request = query.into_inner();
    request.tenant_id = Some(tenant_id);
    
    let response = client_service.list_clients(request).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": response,
        "meta": {
            "tenant_id": tenant_id
        }
    })))
}

// Admin endpoints for client management
#[instrument(skip(client_service))]
pub async fn admin_list_all_clients(
    query: web::Query<ClientListRequest>,
    client_service: web::Data<ServiceClientService>,
) -> Result<HttpResponse, AppError> {
    // This endpoint bypasses tenant isolation for admin users
    let request = query.into_inner();
    
    let response = client_service.list_clients(request).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": response,
        "meta": {
            "admin_view": true,
            "total_clients": response.total
        }
    })))
}

#[instrument(skip(client_service))]
pub async fn admin_get_client_overview(
    client_service: web::Data<ServiceClientService>,
) -> Result<HttpResponse, AppError> {
    // Get overview of all clients for admin dashboard
    let all_clients = client_service.list_clients(ClientListRequest {
        tenant_id: None,
        client_type: None,
        is_active: None,
        is_development: None,
        page: Some(1),
        limit: Some(1000), // Get all clients for overview
        search: None,
    }).await?;

    let mut overview = json!({
        "total_clients": all_clients.total,
        "active_clients": 0,
        "inactive_clients": 0,
        "development_clients": 0,
        "production_clients": 0,
        "client_types": {},
        "clients_by_tenant": {}
    });

    // Calculate statistics
    let mut active_count = 0;
    let mut inactive_count = 0;
    let mut dev_count = 0;
    let mut prod_count = 0;
    let mut type_counts = std::collections::HashMap::new();
    let mut tenant_counts = std::collections::HashMap::new();

    for client in &all_clients.clients {
        if client.is_active {
            active_count += 1;
        } else {
            inactive_count += 1;
        }

        if client.is_development {
            dev_count += 1;
        } else {
            prod_count += 1;
        }

        *type_counts.entry(client.client_type.to_string()).or_insert(0) += 1;
        *tenant_counts.entry(client.tenant_id.to_string()).or_insert(0) += 1;
    }

    overview["active_clients"] = json!(active_count);
    overview["inactive_clients"] = json!(inactive_count);
    overview["development_clients"] = json!(dev_count);
    overview["production_clients"] = json!(prod_count);
    overview["client_types"] = json!(type_counts);
    overview["clients_by_tenant"] = json!(tenant_counts);

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": overview
    })))
}

// Health check endpoint for client service
#[instrument(skip(client_service))]
pub async fn client_health_check(
    client_service: web::Data<ServiceClientService>,
) -> Result<HttpResponse, AppError> {
    // Basic health check for client service
    let client_count = client_service.list_clients(ClientListRequest {
        tenant_id: None,
        client_type: None,
        is_active: Some(true),
        is_development: None,
        page: Some(1),
        limit: Some(1),
        search: None,
    }).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "service": "service_client_service",
        "status": "healthy",
        "active_clients": client_count.total,
        "timestamp": chrono::Utc::now()
    })))
}

pub fn configure_client_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/clients")
            .route("", web::post().to(create_client))
            .route("", web::get().to(list_clients))
            .route("/health", web::get().to(client_health_check))
            .route("/verify", web::post().to(verify_client_credentials))
            .route("/{client_id}", web::get().to(get_client))
            .route("/{client_id}", web::put().to(update_client))
            .route("/{client_id}", web::delete().to(delete_client))
            .route("/{client_id}/regenerate-secret", web::post().to(regenerate_client_secret))
            .route("/id/{id}", web::get().to(get_client_by_id))
    );

    // Tenant-specific client routes
    cfg.service(
        web::scope("/tenants/{tenant_id}/clients")
            .route("", web::get().to(list_tenant_clients))
    );

    // Admin routes (should be protected by admin middleware)
    cfg.service(
        web::scope("/admin/clients")
            .route("", web::get().to(admin_list_all_clients))
            .route("/overview", web::get().to(admin_get_client_overview))
    );
}
