use actix_web::{web, HttpRequest, HttpResponse, Result};
use uuid::Uuid;
use crate::models::{
    CreateTenantRequest, UpdateTenantRequest, TenantListRequest
};
use crate::services::TenantService;
use crate::utils::errors::AppError;
use crate::middleware::extract_user_id;
use tracing::{info, warn, error, instrument};
use serde_json::json;

#[instrument(skip(tenant_service))]
pub async fn create_tenant(
    req: HttpRequest,
    tenant_service: web::Data<TenantService>,
    request: web::Json<CreateTenantRequest>,
) -> Result<HttpResponse, AppError> {
    let created_by = extract_user_id(&req);
    
    info!(
        tenant_name = %request.name,
        domain = %request.domain,
        created_by = ?created_by,
        "Creating new tenant"
    );

    let tenant = tenant_service
        .create_tenant(request.into_inner(), created_by)
        .await?;

    Ok(HttpResponse::Created().json(json!({
        "success": true,
        "message": "Tenant created successfully",
        "data": tenant
    })))
}

#[instrument(skip(tenant_service))]
pub async fn get_tenant(
    path: web::Path<Uuid>,
    tenant_service: web::Data<TenantService>,
) -> Result<HttpResponse, AppError> {
    let tenant_id = path.into_inner();
    
    let tenant = tenant_service.get_tenant(tenant_id).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": tenant
    })))
}

#[instrument(skip(tenant_service))]
pub async fn get_tenant_by_domain(
    path: web::Path<String>,
    tenant_service: web::Data<TenantService>,
) -> Result<HttpResponse, AppError> {
    let domain = path.into_inner();
    
    let tenant = tenant_service.get_tenant_by_domain(&domain).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": tenant
    })))
}

#[instrument(skip(tenant_service))]
pub async fn update_tenant(
    req: HttpRequest,
    path: web::Path<Uuid>,
    tenant_service: web::Data<TenantService>,
    request: web::Json<UpdateTenantRequest>,
) -> Result<HttpResponse, AppError> {
    let tenant_id = path.into_inner();
    let updated_by = extract_user_id(&req);
    
    info!(
        tenant_id = %tenant_id,
        updated_by = ?updated_by,
        "Updating tenant"
    );

    let tenant = tenant_service
        .update_tenant(tenant_id, request.into_inner(), updated_by)
        .await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "Tenant updated successfully",
        "data": tenant
    })))
}

#[instrument(skip(tenant_service))]
pub async fn delete_tenant(
    path: web::Path<Uuid>,
    tenant_service: web::Data<TenantService>,
) -> Result<HttpResponse, AppError> {
    let tenant_id = path.into_inner();
    
    info!(tenant_id = %tenant_id, "Deleting tenant");

    tenant_service.delete_tenant(tenant_id).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "Tenant deleted successfully"
    })))
}

#[instrument(skip(tenant_service))]
pub async fn list_tenants(
    query: web::Query<TenantListRequest>,
    tenant_service: web::Data<TenantService>,
) -> Result<HttpResponse, AppError> {
    let request = query.into_inner();
    
    let response = tenant_service.list_tenants(request).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": response
    })))
}

#[instrument(skip(tenant_service))]
pub async fn get_tenant_stats(
    path: web::Path<Uuid>,
    tenant_service: web::Data<TenantService>,
) -> Result<HttpResponse, AppError> {
    let tenant_id = path.into_inner();
    
    let stats = tenant_service.get_tenant_stats(tenant_id).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": stats
    })))
}

// Admin-only endpoints for tenant management
#[instrument(skip(tenant_service))]
pub async fn admin_list_all_tenants(
    query: web::Query<TenantListRequest>,
    tenant_service: web::Data<TenantService>,
) -> Result<HttpResponse, AppError> {
    // This endpoint bypasses tenant isolation for admin users
    let request = query.into_inner();
    
    let response = tenant_service.list_tenants(request).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": response,
        "meta": {
            "admin_view": true,
            "total_tenants": response.total
        }
    })))
}

#[instrument(skip(tenant_service))]
pub async fn admin_get_tenant_overview(
    tenant_service: web::Data<TenantService>,
) -> Result<HttpResponse, AppError> {
    // Get overview of all tenants for admin dashboard
    let all_tenants = tenant_service.list_tenants(TenantListRequest {
        page: Some(1),
        limit: Some(1000), // Get all tenants for overview
        search: None,
        subscription_tier: None,
        is_active: None,
    }).await?;

    let mut overview = json!({
        "total_tenants": all_tenants.total,
        "active_tenants": 0,
        "inactive_tenants": 0,
        "subscription_tiers": {},
        "total_users": 0,
        "total_clients": 0
    });

    // Calculate statistics
    let mut active_count = 0;
    let mut inactive_count = 0;
    let mut tier_counts = std::collections::HashMap::new();
    let mut total_users = 0;
    let mut total_clients = 0;

    for tenant in &all_tenants.tenants {
        if tenant.is_active {
            active_count += 1;
        } else {
            inactive_count += 1;
        }

        *tier_counts.entry(&tenant.subscription_tier).or_insert(0) += 1;
        
        if let Some(user_count) = tenant.user_count {
            total_users += user_count;
        }
        
        if let Some(client_count) = tenant.client_count {
            total_clients += client_count;
        }
    }

    overview["active_tenants"] = json!(active_count);
    overview["inactive_tenants"] = json!(inactive_count);
    overview["subscription_tiers"] = json!(tier_counts);
    overview["total_users"] = json!(total_users);
    overview["total_clients"] = json!(total_clients);

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": overview
    })))
}

// Health check endpoint that includes tenant information
#[instrument(skip(tenant_service))]
pub async fn tenant_health_check(
    tenant_service: web::Data<TenantService>,
) -> Result<HttpResponse, AppError> {
    // Basic health check for tenant service
    let tenant_count = tenant_service.list_tenants(TenantListRequest {
        page: Some(1),
        limit: Some(1),
        search: None,
        subscription_tier: None,
        is_active: Some(true),
    }).await?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "service": "tenant_service",
        "status": "healthy",
        "active_tenants": tenant_count.total,
        "timestamp": chrono::Utc::now()
    })))
}

pub fn configure_tenant_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/tenants")
            .route("", web::post().to(create_tenant))
            .route("", web::get().to(list_tenants))
            .route("/health", web::get().to(tenant_health_check))
            .route("/domain/{domain}", web::get().to(get_tenant_by_domain))
            .route("/{tenant_id}", web::get().to(get_tenant))
            .route("/{tenant_id}", web::put().to(update_tenant))
            .route("/{tenant_id}", web::delete().to(delete_tenant))
            .route("/{tenant_id}/stats", web::get().to(get_tenant_stats))
    );

    // Admin routes (should be protected by admin middleware)
    cfg.service(
        web::scope("/admin/tenants")
            .route("", web::get().to(admin_list_all_tenants))
            .route("/overview", web::get().to(admin_get_tenant_overview))
    );
}
