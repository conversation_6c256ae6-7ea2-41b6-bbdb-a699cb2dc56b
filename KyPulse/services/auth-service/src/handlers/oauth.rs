use actix_web::{web, HttpRequest, HttpResponse, Result as ActixResult};
use serde_json::json;
use tracing::{info, warn, error};
use validator::Validate;

use crate::services::OAuthService;
use crate::models::oauth::*;
use crate::utils::errors::AppError;

pub fn configure_oauth_routes(cfg: &mut web::ServiceConfig) {
    cfg.route("/oauth/providers", web::get().to(get_oauth_providers))
       .route("/oauth/{provider}", web::get().to(initiate_oauth_flow))
       .route("/oauth/{provider}/callback", web::get().to(handle_oauth_callback))
       .route("/oauth/link", web::post().to(link_oauth_account))
       .route("/oauth/unlink/{provider}", web::delete().to(unlink_oauth_account));
}

/// Get available OAuth providers
pub async fn get_oauth_providers(
    oauth_service: web::Data<OAuthService>,
) -> ActixResult<HttpResponse, AppError> {
    info!("Getting available OAuth providers");

    match oauth_service.get_oauth_providers().await {
        Ok(response) => {
            info!("Successfully retrieved OAuth providers");
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            error!("Failed to get OAuth providers: {}", e);
            Ok(HttpResponse::InternalServerError().json(json!({
                "success": false,
                "message": "Failed to retrieve OAuth providers",
                "error": e.to_string()
            })))
        }
    }
}

/// Initiate OAuth flow
pub async fn initiate_oauth_flow(
    path: web::Path<String>,
    query: web::Query<std::collections::HashMap<String, String>>,
    oauth_service: web::Data<OAuthService>,
) -> ActixResult<HttpResponse, AppError> {
    let provider = path.into_inner();
    info!("Initiating OAuth flow for provider: {}", provider);

    let request = OAuthInitiateRequest {
        provider: provider.clone(),
        redirect_uri: query.get("redirect_uri").cloned(),
        state: query.get("state").cloned(),
    };

    // Validate request
    if let Err(validation_errors) = request.validate() {
        warn!("Invalid OAuth initiate request for provider {}: {:?}", provider, validation_errors);
        return Ok(HttpResponse::BadRequest().json(json!({
            "success": false,
            "message": "Invalid request parameters",
            "errors": validation_errors
        })));
    }

    match oauth_service.initiate_oauth_flow(request).await {
        Ok(response) => {
            info!("Successfully initiated OAuth flow for provider: {}", provider);
            // Redirect to the OAuth provider's authorization URL
            let auth_url = response.authorization_url.clone();
            Ok(HttpResponse::Found()
                .append_header(("Location", auth_url))
                .json(response))
        }
        Err(e) => {
            error!("Failed to initiate OAuth flow for provider {}: {}", provider, e);
            Ok(HttpResponse::BadRequest().json(json!({
                "success": false,
                "message": format!("Failed to initiate OAuth flow: {}", e),
                "error": e.to_string()
            })))
        }
    }
}

/// Handle OAuth callback
pub async fn handle_oauth_callback(
    path: web::Path<String>,
    query: web::Query<std::collections::HashMap<String, String>>,
    oauth_service: web::Data<OAuthService>,
) -> ActixResult<HttpResponse, AppError> {
    let provider = path.into_inner();
    info!("Handling OAuth callback for provider: {}", provider);

    // Extract required parameters from query
    let code = match query.get("code") {
        Some(code) => code.clone(),
        None => {
            warn!("Missing authorization code in OAuth callback for provider: {}", provider);
            return Ok(HttpResponse::BadRequest().json(json!({
                "success": false,
                "message": "Missing authorization code"
            })));
        }
    };

    let state = match query.get("state") {
        Some(state) => state.clone(),
        None => {
            warn!("Missing state parameter in OAuth callback for provider: {}", provider);
            return Ok(HttpResponse::BadRequest().json(json!({
                "success": false,
                "message": "Missing state parameter"
            })));
        }
    };

    let request = OAuthCallbackRequest {
        provider: provider.clone(),
        code,
        state,
        code_verifier: query.get("code_verifier").cloned(),
    };

    // Validate request
    if let Err(validation_errors) = request.validate() {
        warn!("Invalid OAuth callback request for provider {}: {:?}", provider, validation_errors);
        return Ok(HttpResponse::BadRequest().json(json!({
            "success": false,
            "message": "Invalid callback parameters",
            "errors": validation_errors
        })));
    }

    match oauth_service.handle_oauth_callback(request).await {
        Ok(response) => {
            info!("Successfully handled OAuth callback for provider: {}", provider);
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            error!("Failed to handle OAuth callback for provider {}: {}", provider, e);
            Ok(HttpResponse::BadRequest().json(json!({
                "success": false,
                "message": format!("OAuth authentication failed: {}", e),
                "error": e.to_string()
            })))
        }
    }
}

/// Link OAuth account to existing user
pub async fn link_oauth_account(
    request_body: web::Json<OAuthLinkRequest>,
    oauth_service: web::Data<OAuthService>,
) -> ActixResult<HttpResponse, AppError> {
    let request = request_body.into_inner();
    info!("Linking OAuth account for provider: {} to user: {}", request.provider, request.user_id);

    // Validate request
    if let Err(validation_errors) = request.validate() {
        warn!("Invalid OAuth link request: {:?}", validation_errors);
        return Ok(HttpResponse::BadRequest().json(json!({
            "success": false,
            "message": "Invalid request parameters",
            "errors": validation_errors
        })));
    }

    match oauth_service.link_oauth_account(request).await {
        Ok(response) => {
            info!("Successfully linked OAuth account");
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            error!("Failed to link OAuth account: {}", e);
            let mut status_code = match e {
                OAuthError::AccountAlreadyLinked => HttpResponse::Conflict(),
                OAuthError::InvalidState | OAuthError::InvalidCode => HttpResponse::BadRequest(),
                _ => HttpResponse::InternalServerError(),
            };

            Ok(status_code.json(json!({
                "success": false,
                "message": format!("Failed to link OAuth account: {}", e),
                "error": e.to_string()
            })))
        }
    }
}

/// Unlink OAuth account from user
pub async fn unlink_oauth_account(
    path: web::Path<String>,
    query: web::Query<std::collections::HashMap<String, String>>,
    oauth_service: web::Data<OAuthService>,
) -> ActixResult<HttpResponse, AppError> {
    let provider = path.into_inner();
    info!("Unlinking OAuth account for provider: {}", provider);

    // Extract user_id from query parameters or JWT token
    let user_id = match query.get("user_id") {
        Some(user_id_str) => {
            match user_id_str.parse() {
                Ok(user_id) => user_id,
                Err(_) => {
                    warn!("Invalid user_id format in unlink request");
                    return Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Invalid user_id format"
                    })));
                }
            }
        }
        None => {
            warn!("Missing user_id in OAuth unlink request");
            return Ok(HttpResponse::BadRequest().json(json!({
                "success": false,
                "message": "Missing user_id parameter"
            })));
        }
    };

    let request = OAuthUnlinkRequest {
        provider: provider.clone(),
        user_id,
    };

    // Validate request
    if let Err(validation_errors) = request.validate() {
        warn!("Invalid OAuth unlink request: {:?}", validation_errors);
        return Ok(HttpResponse::BadRequest().json(json!({
            "success": false,
            "message": "Invalid request parameters",
            "errors": validation_errors
        })));
    }

    match oauth_service.unlink_oauth_account(request).await {
        Ok(response) => {
            info!("Successfully unlinked OAuth account for provider: {}", provider);
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            error!("Failed to unlink OAuth account for provider {}: {}", provider, e);
            let mut status_code = match e {
                OAuthError::AccountNotFound => HttpResponse::NotFound(),
                _ => HttpResponse::InternalServerError(),
            };

            Ok(status_code.json(json!({
                "success": false,
                "message": format!("Failed to unlink OAuth account: {}", e),
                "error": e.to_string()
            })))
        }
    }
}
