use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthAccount {
    pub id: Uuid,
    pub user_id: Uuid,
    pub provider: String,
    pub provider_user_id: String,
    pub email: Option<String>,
    pub access_token: Option<String>,
    pub refresh_token: Option<String>,
    pub expires_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthState {
    pub id: Uuid,
    pub state: String,
    pub code_verifier: Option<String>,
    pub redirect_uri: String,
    pub provider: String,
    pub expires_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct OAuthInitiateRequest {
    #[validate(length(min = 1, max = 50))]
    pub provider: String,
    #[validate(url)]
    pub redirect_uri: Option<String>,
    pub state: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthInitiateResponse {
    pub success: bool,
    pub authorization_url: String,
    pub state: String,
    pub code_verifier: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct OAuthCallbackRequest {
    #[validate(length(min = 1, max = 50))]
    pub provider: String,
    #[validate(length(min = 1))]
    pub code: String,
    #[validate(length(min = 1))]
    pub state: String,
    pub code_verifier: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthCallbackResponse {
    pub success: bool,
    pub user_id: Option<Uuid>,
    pub access_token: Option<String>,
    pub refresh_token: Option<String>,
    pub is_new_user: bool,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct OAuthLinkRequest {
    #[validate(length(min = 1, max = 50))]
    pub provider: String,
    #[validate(length(min = 1))]
    pub code: String,
    #[validate(length(min = 1))]
    pub state: String,
    pub user_id: Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthLinkResponse {
    pub success: bool,
    pub message: String,
    pub oauth_account_id: Option<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct OAuthUnlinkRequest {
    #[validate(length(min = 1, max = 50))]
    pub provider: String,
    pub user_id: Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthUnlinkResponse {
    pub success: bool,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthProvidersResponse {
    pub success: bool,
    pub providers: Vec<OAuthProviderInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthProviderInfo {
    pub name: String,
    pub display_name: String,
    pub enabled: bool,
    pub authorization_url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthUserInfo {
    pub id: String,
    pub email: Option<String>,
    pub name: Option<String>,
    pub picture: Option<String>,
    pub verified_email: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthTokenResponse {
    pub access_token: String,
    pub refresh_token: Option<String>,
    pub expires_in: Option<i64>,
    pub token_type: String,
    pub scope: Option<String>,
}

// Error types for OAuth operations
#[derive(Debug, thiserror::Error)]
pub enum OAuthError {
    #[error("Invalid OAuth provider: {0}")]
    InvalidProvider(String),
    
    #[error("Invalid OAuth state parameter")]
    InvalidState,
    
    #[error("OAuth state expired")]
    StateExpired,
    
    #[error("Invalid authorization code")]
    InvalidCode,
    
    #[error("Invalid redirect URI")]
    InvalidRedirectUri,
    
    #[error("OAuth provider error: {0}")]
    ProviderError(String),
    
    #[error("Token exchange failed: {0}")]
    TokenExchangeFailed(String),
    
    #[error("User info retrieval failed: {0}")]
    UserInfoFailed(String),
    
    #[error("OAuth account already linked")]
    AccountAlreadyLinked,
    
    #[error("OAuth account not found")]
    AccountNotFound,
    
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
    
    #[error("HTTP client error: {0}")]
    HttpError(#[from] reqwest::Error),
    
    #[error("JSON parsing error: {0}")]
    JsonError(#[from] serde_json::Error),
    
    #[error("URL parsing error: {0}")]
    UrlError(#[from] url::ParseError),
    
    #[error("OAuth2 error: {0}")]
    OAuth2Error(String),
}

// OAuth provider enum for better type safety and dyn compatibility
#[derive(Debug, Clone)]
pub enum OAuthProviderType {
    Google,
    GitHub,
    Microsoft,
    Apple,
}

// PKCE helper functions
pub fn generate_code_verifier() -> String {
    use base64::Engine;
    let mut bytes = [0u8; 32];
    rand::Rng::fill(&mut rand::thread_rng(), &mut bytes);
    base64::engine::general_purpose::URL_SAFE_NO_PAD.encode(bytes)
}

pub fn generate_code_challenge(verifier: &str) -> String {
    use sha2::{Digest, Sha256};
    use base64::Engine;
    
    let digest = Sha256::digest(verifier.as_bytes());
    base64::engine::general_purpose::URL_SAFE_NO_PAD.encode(digest)
}

pub fn generate_state() -> String {
    use base64::Engine;
    let mut bytes = [0u8; 32];
    rand::Rng::fill(&mut rand::thread_rng(), &mut bytes);
    base64::engine::general_purpose::URL_SAFE_NO_PAD.encode(bytes)
}
