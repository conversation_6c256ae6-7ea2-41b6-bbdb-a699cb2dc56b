use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Tenant {
    pub id: Uuid,
    pub name: String,
    pub domain: String,
    pub settings: serde_json::Value,
    pub is_active: bool,
    pub subscription_tier: String,
    pub max_users: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTenantRequest {
    pub name: String,
    pub domain: String,
    pub settings: Option<TenantSettings>,
    pub subscription_tier: Option<String>,
    pub max_users: Option<i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTenantRequest {
    pub name: Option<String>,
    pub domain: Option<String>,
    pub settings: Option<TenantSettings>,
    pub subscription_tier: Option<String>,
    pub max_users: Option<i32>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TenantResponse {
    pub id: Uuid,
    pub name: String,
    pub domain: String,
    pub settings: TenantSettings,
    pub is_active: bool,
    pub subscription_tier: String,
    pub max_users: i32,
    pub user_count: Option<i32>,
    pub client_count: Option<i32>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TenantSettings {
    pub oauth_providers: Vec<String>,
    pub mfa_required: bool,
    pub session_timeout: String, // Duration as string (e.g., "24h", "30m")
    pub password_policy: PasswordPolicy,
    pub branding: BrandingConfig,
    pub security: SecuritySettings,
    pub features: TenantFeatures,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasswordPolicy {
    pub min_length: u32,
    pub require_uppercase: bool,
    pub require_lowercase: bool,
    pub require_numbers: bool,
    pub require_special_chars: bool,
    pub prevent_reuse_count: u32,
    pub max_age_days: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrandingConfig {
    pub logo_url: Option<String>,
    pub primary_color: String,
    pub secondary_color: String,
    pub company_name: String,
    pub support_email: String,
    pub privacy_policy_url: Option<String>,
    pub terms_of_service_url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecuritySettings {
    pub max_login_attempts: u32,
    pub lockout_duration_minutes: u32,
    pub require_email_verification: bool,
    pub allow_password_reset: bool,
    pub session_timeout_minutes: u32,
    pub max_concurrent_sessions: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TenantFeatures {
    pub oauth_enabled: bool,
    pub mfa_enabled: bool,
    pub sms_totp_enabled: bool,
    pub rbac_enabled: bool,
    pub audit_logging_enabled: bool,
    pub api_access_enabled: bool,
    pub webhook_enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TenantStats {
    pub tenant_id: Uuid,
    pub tenant_name: String,
    pub domain: String,
    pub subscription_tier: String,
    pub user_count: Option<i64>,
    pub client_count: Option<i64>,
    pub active_sessions: Option<i64>,
    pub max_users: i32,
    pub tenant_active: bool,
    pub tenant_created_at: DateTime<Utc>,
}

impl Default for TenantSettings {
    fn default() -> Self {
        Self {
            oauth_providers: vec!["google".to_string(), "github".to_string()],
            mfa_required: false,
            session_timeout: "24h".to_string(),
            password_policy: PasswordPolicy::default(),
            branding: BrandingConfig::default(),
            security: SecuritySettings::default(),
            features: TenantFeatures::default(),
        }
    }
}

impl Default for PasswordPolicy {
    fn default() -> Self {
        Self {
            min_length: 8,
            require_uppercase: true,
            require_lowercase: true,
            require_numbers: true,
            require_special_chars: true,
            prevent_reuse_count: 5,
            max_age_days: Some(90),
        }
    }
}

impl Default for BrandingConfig {
    fn default() -> Self {
        Self {
            logo_url: None,
            primary_color: "#3B82F6".to_string(),
            secondary_color: "#64748B".to_string(),
            company_name: "KyPulse".to_string(),
            support_email: "<EMAIL>".to_string(),
            privacy_policy_url: None,
            terms_of_service_url: None,
        }
    }
}

impl Default for SecuritySettings {
    fn default() -> Self {
        Self {
            max_login_attempts: 5,
            lockout_duration_minutes: 15,
            require_email_verification: true,
            allow_password_reset: true,
            session_timeout_minutes: 1440, // 24 hours
            max_concurrent_sessions: 5,
        }
    }
}

impl Default for TenantFeatures {
    fn default() -> Self {
        Self {
            oauth_enabled: true,
            mfa_enabled: true,
            sms_totp_enabled: true,
            rbac_enabled: true,
            audit_logging_enabled: true,
            api_access_enabled: true,
            webhook_enabled: false,
        }
    }
}

impl From<Tenant> for TenantResponse {
    fn from(tenant: Tenant) -> Self {
        let settings: TenantSettings = serde_json::from_value(tenant.settings)
            .unwrap_or_default();

        Self {
            id: tenant.id,
            name: tenant.name,
            domain: tenant.domain,
            settings,
            is_active: tenant.is_active,
            subscription_tier: tenant.subscription_tier,
            max_users: tenant.max_users,
            user_count: None, // Will be populated by service layer
            client_count: None, // Will be populated by service layer
            created_at: tenant.created_at,
            updated_at: tenant.updated_at,
        }
    }
}

impl TenantSettings {
    pub fn to_json(&self) -> serde_json::Value {
        serde_json::to_value(self).unwrap_or(serde_json::Value::Object(serde_json::Map::new()))
    }

    pub fn from_json(value: &serde_json::Value) -> Self {
        serde_json::from_value(value.clone()).unwrap_or_default()
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TenantListRequest {
    pub page: Option<u32>,
    pub limit: Option<u32>,
    pub search: Option<String>,
    pub subscription_tier: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TenantListResponse {
    pub tenants: Vec<TenantResponse>,
    pub total: u64,
    pub page: u32,
    pub limit: u32,
    pub total_pages: u32,
}
