use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ServiceClient {
    pub id: Uuid,
    pub client_id: String,
    pub client_secret_hash: String,
    pub client_name: String,
    pub client_type: String,
    pub tenant_id: Uuid,
    pub allowed_origins: Vec<String>,
    pub redirect_uris: Vec<String>,
    pub oauth_providers: Vec<String>,
    pub scopes: Vec<String>,
    pub features: serde_json::Value,
    pub security_settings: serde_json::Value,
    pub is_active: bool,
    pub is_development: bool,
    pub rate_limit_requests_per_minute: i32,
    pub rate_limit_requests_per_hour: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
    pub last_used_at: Option<DateTime<Utc>>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateServiceClientRequest {
    pub client_name: String,
    pub client_type: ClientType,
    pub tenant_id: Uuid,
    pub allowed_origins: Vec<String>,
    pub redirect_uris: Vec<String>,
    pub oauth_providers: Vec<String>,
    pub scopes: Option<Vec<String>>,
    pub features: Option<ClientFeatures>,
    pub security_settings: Option<ClientSecuritySettings>,
    pub is_development: Option<bool>,
    pub rate_limits: Option<RateLimits>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateServiceClientRequest {
    pub client_name: Option<String>,
    pub allowed_origins: Option<Vec<String>>,
    pub redirect_uris: Option<Vec<String>>,
    pub oauth_providers: Option<Vec<String>>,
    pub scopes: Option<Vec<String>>,
    pub features: Option<ClientFeatures>,
    pub security_settings: Option<ClientSecuritySettings>,
    pub is_active: Option<bool>,
    pub is_development: Option<bool>,
    pub rate_limits: Option<RateLimits>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceClientResponse {
    pub id: Uuid,
    pub client_id: String,
    pub client_name: String,
    pub client_type: ClientType,
    pub tenant_id: Uuid,
    pub tenant_name: Option<String>,
    pub allowed_origins: Vec<String>,
    pub redirect_uris: Vec<String>,
    pub oauth_providers: Vec<String>,
    pub scopes: Vec<String>,
    pub features: ClientFeatures,
    pub security_settings: ClientSecuritySettings,
    pub is_active: bool,
    pub is_development: bool,
    pub rate_limits: RateLimits,
    pub usage_stats: Option<ClientUsageStats>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_used_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceClientWithSecret {
    pub client: ServiceClientResponse,
    pub client_secret: String, // Only returned on creation
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum ClientType {
    Web,
    Mobile,
    Service,
    Api,
}

impl std::fmt::Display for ClientType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ClientType::Web => write!(f, "web"),
            ClientType::Mobile => write!(f, "mobile"),
            ClientType::Service => write!(f, "service"),
            ClientType::Api => write!(f, "api"),
        }
    }
}

impl std::str::FromStr for ClientType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "web" => Ok(ClientType::Web),
            "mobile" => Ok(ClientType::Mobile),
            "service" => Ok(ClientType::Service),
            "api" => Ok(ClientType::Api),
            _ => Err(format!("Invalid client type: {}", s)),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientFeatures {
    pub oauth_enabled: bool,
    pub mfa_required: bool,
    pub sms_totp_enabled: bool,
    pub email_verification_required: bool,
    pub password_reset_enabled: bool,
    pub session_management_enabled: bool,
    pub rbac_enabled: bool,
    pub audit_logging_enabled: bool,
    pub webhook_enabled: bool,
    pub api_key_auth_enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientSecuritySettings {
    pub require_https: bool,
    pub session_timeout_minutes: u32,
    pub refresh_token_timeout_hours: u32,
    pub max_concurrent_sessions: u32,
    pub require_pkce: bool, // For OAuth PKCE
    pub allowed_grant_types: Vec<String>,
    pub token_endpoint_auth_method: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimits {
    pub requests_per_minute: u32,
    pub requests_per_hour: u32,
    pub login_attempts_per_minute: u32,
    pub password_reset_per_hour: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientUsageStats {
    pub total_requests: u64,
    pub requests_last_24h: u64,
    pub active_users: u64,
    pub active_sessions: u64,
    pub last_request_at: Option<DateTime<Utc>>,
}

impl Default for ClientFeatures {
    fn default() -> Self {
        Self {
            oauth_enabled: true,
            mfa_required: false,
            sms_totp_enabled: true,
            email_verification_required: true,
            password_reset_enabled: true,
            session_management_enabled: true,
            rbac_enabled: true,
            audit_logging_enabled: true,
            webhook_enabled: false,
            api_key_auth_enabled: false,
        }
    }
}

impl Default for ClientSecuritySettings {
    fn default() -> Self {
        Self {
            require_https: true,
            session_timeout_minutes: 1440, // 24 hours
            refresh_token_timeout_hours: 168, // 7 days
            max_concurrent_sessions: 5,
            require_pkce: true,
            allowed_grant_types: vec![
                "authorization_code".to_string(),
                "refresh_token".to_string(),
            ],
            token_endpoint_auth_method: "client_secret_post".to_string(),
        }
    }
}

impl Default for RateLimits {
    fn default() -> Self {
        Self {
            requests_per_minute: 1000,
            requests_per_hour: 10000,
            login_attempts_per_minute: 10,
            password_reset_per_hour: 5,
        }
    }
}

impl From<ServiceClient> for ServiceClientResponse {
    fn from(client: ServiceClient) -> Self {
        let features: ClientFeatures = serde_json::from_value(client.features)
            .unwrap_or_default();
        let security_settings: ClientSecuritySettings = serde_json::from_value(client.security_settings)
            .unwrap_or_default();

        Self {
            id: client.id,
            client_id: client.client_id,
            client_name: client.client_name,
            client_type: client.client_type.parse().unwrap_or(ClientType::Web),
            tenant_id: client.tenant_id,
            tenant_name: None, // Will be populated by service layer
            allowed_origins: client.allowed_origins,
            redirect_uris: client.redirect_uris,
            oauth_providers: client.oauth_providers,
            scopes: client.scopes,
            features,
            security_settings,
            is_active: client.is_active,
            is_development: client.is_development,
            rate_limits: RateLimits {
                requests_per_minute: client.rate_limit_requests_per_minute as u32,
                requests_per_hour: client.rate_limit_requests_per_hour as u32,
                login_attempts_per_minute: 10, // Default, could be in security_settings
                password_reset_per_hour: 5, // Default, could be in security_settings
            },
            usage_stats: None, // Will be populated by service layer
            created_at: client.created_at,
            updated_at: client.updated_at,
            last_used_at: client.last_used_at,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientListRequest {
    pub tenant_id: Option<Uuid>,
    pub client_type: Option<ClientType>,
    pub is_active: Option<bool>,
    pub is_development: Option<bool>,
    pub page: Option<u32>,
    pub limit: Option<u32>,
    pub search: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientListResponse {
    pub clients: Vec<ServiceClientResponse>,
    pub total: u64,
    pub page: u32,
    pub limit: u32,
    pub total_pages: u32,
}

// Client context for middleware
#[derive(Debug, Clone)]
pub struct ClientContext {
    pub client_id: String,
    pub client_name: String,
    pub client_type: ClientType,
    pub tenant_id: Uuid,
    pub allowed_origins: Vec<String>,
    pub oauth_providers: Vec<String>,
    pub features: ClientFeatures,
    pub security_settings: ClientSecuritySettings,
    pub rate_limits: RateLimits,
}

impl From<ServiceClient> for ClientContext {
    fn from(client: ServiceClient) -> Self {
        let features: ClientFeatures = serde_json::from_value(client.features)
            .unwrap_or_default();
        let security_settings: ClientSecuritySettings = serde_json::from_value(client.security_settings)
            .unwrap_or_default();

        Self {
            client_id: client.client_id,
            client_name: client.client_name,
            client_type: client.client_type.parse().unwrap_or(ClientType::Web),
            tenant_id: client.tenant_id,
            allowed_origins: client.allowed_origins,
            oauth_providers: client.oauth_providers,
            features,
            security_settings,
            rate_limits: RateLimits {
                requests_per_minute: client.rate_limit_requests_per_minute as u32,
                requests_per_hour: client.rate_limit_requests_per_hour as u32,
                login_attempts_per_minute: 10,
                password_reset_per_hour: 5,
            },
        }
    }
}
