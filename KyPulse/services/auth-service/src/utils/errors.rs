use actix_web::{HttpResponse, ResponseError};
use thiserror::Error;

#[derive(Error, Debug)]
pub enum AuthError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("Invalid credentials")]
    InvalidCredentials,
    
    #[error("User not found")]
    UserNotFound,
    
    #[error("User already exists")]
    UserAlreadyExists,
    
    #[error("Invalid token")]
    InvalidToken,
    
    #[error("Internal server error")]
    InternalServerError,
}

impl ResponseError for AuthError {
    fn error_response(&self) -> HttpResponse {
        match self {
            AuthError::InvalidCredentials => HttpResponse::Unauthorized().json("Invalid credentials"),
            AuthError::UserNotFound => HttpResponse::NotFound().json("User not found"),
            AuthError::UserAlreadyExists => HttpResponse::Conflict().json("User already exists"),
            AuthError::InvalidToken => HttpResponse::Unauthorized().json("Invalid token"),
            AuthError::Database(_) | AuthError::InternalServerError => {
                HttpResponse::InternalServerError().json("Internal server error")
            }
        }
    }
}

#[derive(Error, Debug)]
pub enum AppError {
    #[error("Bad request: {0}")]
    BadRequest(String),

    #[error("Unauthorized: {0}")]
    Unauthorized(String),

    #[error("Forbidden: {0}")]
    Forbidden(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Conflict: {0}")]
    Conflict(String),

    #[error("Too many requests: {0}")]
    TooManyRequests(String),

    #[error("Database error: {0}")]
    DatabaseError(String),

    #[error("Internal server error: {0}")]
    InternalServerError(String),
}

impl ResponseError for AppError {
    fn error_response(&self) -> HttpResponse {
        match self {
            AppError::BadRequest(msg) => HttpResponse::BadRequest().json(msg),
            AppError::Unauthorized(msg) => HttpResponse::Unauthorized().json(msg),
            AppError::Forbidden(msg) => HttpResponse::Forbidden().json(msg),
            AppError::NotFound(msg) => HttpResponse::NotFound().json(msg),
            AppError::Conflict(msg) => HttpResponse::Conflict().json(msg),
            AppError::TooManyRequests(msg) => HttpResponse::TooManyRequests().json(msg),
            AppError::DatabaseError(msg) => HttpResponse::InternalServerError().json(msg),
            AppError::InternalServerError(msg) => HttpResponse::InternalServerError().json(msg),
        }
    }
}
