use std::collections::HashMap;
use tracing::{info, warn, error};
use resend_rs::Resend;

use crate::config::EmailConfig;

#[derive(Debug, Clone)]
pub enum EmailProvider {
    Resend(Resend),
    Smtp(SmtpClient),
}

#[derive(Debug, <PERSON>lone)]
pub struct SmtpClient {
    host: String,
    port: u16,
    username: String,
    password: String,
}

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct EmailService {
    provider: EmailProvider,
    config: EmailConfig,
    templates: EmailTemplateManager,
}

#[derive(Debug, Clone)]
pub struct EmailTemplateManager {
    templates: HashMap<String, EmailTemplate>,
}

#[derive(Debug, Clone)]
pub struct EmailTemplate {
    pub subject: String,
    pub html_body: String,
    pub text_body: String,
}

#[derive(Debug, thiserror::Error)]
pub enum EmailError {
    #[error("Email provider error: {0}")]
    ProviderError(String),
    
    #[error("Template not found: {0}")]
    TemplateNotFound(String),
    
    #[error("Template rendering error: {0}")]
    TemplateRenderError(String),
    
    #[error("Invalid email address: {0}")]
    InvalidEmailAddress(String),
    
    #[error("Email sending failed: {0}")]
    SendingFailed(String),
    
    #[error("Configuration error: {0}")]
    ConfigurationError(String),
}

impl EmailService {
    pub fn new(config: EmailConfig) -> Result<Self, EmailError> {
        let provider = match config.provider.as_str() {
            "resend" => {
                if config.resend_api_key.is_empty() {
                    return Err(EmailError::ConfigurationError("RESEND API key is required".to_string()));
                }
                let resend = Resend::new(&config.resend_api_key);
                EmailProvider::Resend(resend)
            }
            "smtp" => {
                EmailProvider::Smtp(SmtpClient {
                    host: config.smtp.host.clone(),
                    port: config.smtp.port,
                    username: config.smtp.user.clone(),
                    password: config.smtp.password.clone(),
                })
            }
            _ => return Err(EmailError::ConfigurationError(format!("Unsupported email provider: {}", config.provider))),
        };

        let templates = EmailTemplateManager::new();

        Ok(Self {
            provider,
            config,
            templates,
        })
    }

    /// Send email verification email
    pub async fn send_verification_email(
        &self,
        to_email: &str,
        username: &str,
        verification_token: &str,
    ) -> Result<(), EmailError> {
        info!("Sending verification email to: {}", to_email);

        let template = self.templates.get_template("email_verification")?;
        let verification_url = format!("{}/auth/verify-email?token={}", 
            self.get_base_url(), verification_token);

        let context = EmailContext {
            username: username.to_string(),
            verification_url,
            verification_token: verification_token.to_string(),
            company_name: "KyPulse".to_string(),
            support_email: self.config.reply_to.clone(),
        };

        let rendered_template = self.templates.render_template(template, &context)?;

        self.send_email(
            to_email,
            &rendered_template.subject,
            &rendered_template.html_body,
            Some(&rendered_template.text_body),
        ).await
    }

    /// Send password reset email
    pub async fn send_password_reset_email(
        &self,
        to_email: &str,
        username: &str,
        reset_token: &str,
    ) -> Result<(), EmailError> {
        info!("Sending password reset email to: {}", to_email);

        let template = self.templates.get_template("password_reset")?;
        let reset_url = format!("{}/auth/reset-password?token={}", 
            self.get_base_url(), reset_token);

        let context = EmailContext {
            username: username.to_string(),
            verification_url: reset_url.clone(),
            verification_token: reset_token.to_string(),
            company_name: "KyPulse".to_string(),
            support_email: self.config.reply_to.clone(),
        };

        let rendered_template = self.templates.render_template(template, &context)?;

        self.send_email(
            to_email,
            &rendered_template.subject,
            &rendered_template.html_body,
            Some(&rendered_template.text_body),
        ).await
    }

    /// Send MFA setup notification email
    pub async fn send_mfa_setup_email(
        &self,
        to_email: &str,
        username: &str,
    ) -> Result<(), EmailError> {
        info!("Sending MFA setup notification email to: {}", to_email);

        let template = self.templates.get_template("mfa_setup")?;

        let context = EmailContext {
            username: username.to_string(),
            verification_url: String::new(),
            verification_token: String::new(),
            company_name: "KyPulse".to_string(),
            support_email: self.config.reply_to.clone(),
        };

        let rendered_template = self.templates.render_template(template, &context)?;

        self.send_email(
            to_email,
            &rendered_template.subject,
            &rendered_template.html_body,
            Some(&rendered_template.text_body),
        ).await
    }

    /// Send OAuth account linking confirmation email
    pub async fn send_oauth_link_email(
        &self,
        to_email: &str,
        username: &str,
        provider: &str,
    ) -> Result<(), EmailError> {
        info!("Sending OAuth link confirmation email to: {}", to_email);

        let template = self.templates.get_template("oauth_link")?;

        let context = EmailContext {
            username: username.to_string(),
            verification_url: provider.to_string(), // Reuse this field for provider name
            verification_token: String::new(),
            company_name: "KyPulse".to_string(),
            support_email: self.config.reply_to.clone(),
        };

        let rendered_template = self.templates.render_template(template, &context)?;

        self.send_email(
            to_email,
            &rendered_template.subject,
            &rendered_template.html_body,
            Some(&rendered_template.text_body),
        ).await
    }

    /// Generic email sending method
    async fn send_email(
        &self,
        to_email: &str,
        subject: &str,
        html_body: &str,
        text_body: Option<&str>,
    ) -> Result<(), EmailError> {
        match &self.provider {
            EmailProvider::Resend(resend) => {
                self.send_via_resend(resend, to_email, subject, html_body, text_body).await
            }
            EmailProvider::Smtp(smtp_client) => {
                self.send_via_smtp(smtp_client, to_email, subject, html_body, text_body).await
            }
        }
    }

    async fn send_via_resend(
        &self,
        resend: &Resend,
        to_email: &str,
        subject: &str,
        html_body: &str,
        text_body: Option<&str>,
    ) -> Result<(), EmailError> {
        // For now, we'll use a simple approach since the RESEND API structure may have changed
        // This is a placeholder implementation that logs the email instead of sending
        info!("Would send email via RESEND:");
        info!("  To: {}", to_email);
        info!("  Subject: {}", subject);
        info!("  HTML Body: {}", html_body);
        if let Some(text) = text_body {
            info!("  Text Body: {}", text);
        }

        // TODO: Implement actual RESEND API call once we verify the correct API structure
        warn!("RESEND email sending not yet implemented, email logged instead");
        Ok(())
    }

    async fn send_via_smtp(
        &self,
        _smtp_client: &SmtpClient,
        _to_email: &str,
        _subject: &str,
        _html_body: &str,
        _text_body: Option<&str>,
    ) -> Result<(), EmailError> {
        // TODO: Implement SMTP sending as fallback
        warn!("SMTP email sending not yet implemented, falling back to logging");
        info!("Would send email via SMTP to: {}", _to_email);
        Ok(())
    }

    fn get_base_url(&self) -> &str {
        // This should come from configuration
        "http://localhost:3000"
    }
}

#[derive(Debug, Clone)]
struct EmailContext {
    username: String,
    verification_url: String,
    verification_token: String,
    company_name: String,
    support_email: String,
}

impl EmailTemplateManager {
    fn new() -> Self {
        let mut templates = HashMap::new();

        // Email verification template
        templates.insert("email_verification".to_string(), EmailTemplate {
            subject: "Verify your KyPulse account".to_string(),
            html_body: include_str!("../templates/email_verification.html").to_string(),
            text_body: include_str!("../templates/email_verification.txt").to_string(),
        });

        // Password reset template
        templates.insert("password_reset".to_string(), EmailTemplate {
            subject: "Reset your KyPulse password".to_string(),
            html_body: include_str!("../templates/password_reset.html").to_string(),
            text_body: include_str!("../templates/password_reset.txt").to_string(),
        });

        // MFA setup template
        templates.insert("mfa_setup".to_string(), EmailTemplate {
            subject: "Multi-Factor Authentication enabled".to_string(),
            html_body: include_str!("../templates/mfa_setup.html").to_string(),
            text_body: include_str!("../templates/mfa_setup.txt").to_string(),
        });

        // OAuth link template
        templates.insert("oauth_link".to_string(), EmailTemplate {
            subject: "OAuth account linked".to_string(),
            html_body: include_str!("../templates/oauth_link.html").to_string(),
            text_body: include_str!("../templates/oauth_link.txt").to_string(),
        });

        Self { templates }
    }

    fn get_template(&self, template_name: &str) -> Result<&EmailTemplate, EmailError> {
        self.templates.get(template_name)
            .ok_or_else(|| EmailError::TemplateNotFound(template_name.to_string()))
    }

    fn render_template(&self, template: &EmailTemplate, context: &EmailContext) -> Result<EmailTemplate, EmailError> {
        let rendered_subject = self.render_string(&template.subject, context)?;
        let rendered_html = self.render_string(&template.html_body, context)?;
        let rendered_text = self.render_string(&template.text_body, context)?;

        Ok(EmailTemplate {
            subject: rendered_subject,
            html_body: rendered_html,
            text_body: rendered_text,
        })
    }

    fn render_string(&self, template_str: &str, context: &EmailContext) -> Result<String, EmailError> {
        let rendered = template_str
            .replace("{{username}}", &context.username)
            .replace("{{verification_url}}", &context.verification_url)
            .replace("{{verification_token}}", &context.verification_token)
            .replace("{{company_name}}", &context.company_name)
            .replace("{{support_email}}", &context.support_email);

        Ok(rendered)
    }
}
