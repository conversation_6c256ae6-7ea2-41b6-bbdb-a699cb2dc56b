use sqlx::PgPool;
use uuid::Uuid;
use crate::models::{
    Tenant, TenantResponse, CreateTenantRequest, UpdateTenantRequest,
    TenantListRequest, TenantListResponse, TenantSettings, TenantStats
};
use crate::utils::errors::AppError;
use tracing::{info, warn, error, instrument};

pub struct TenantService {
    db_pool: PgPool,
}

impl TenantService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    #[instrument(skip(self))]
    pub async fn create_tenant(
        &self,
        request: CreateTenantRequest,
        created_by: Option<Uuid>,
    ) -> Result<TenantResponse, AppError> {
        // Validate domain uniqueness
        let existing_tenant = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tenants WHERE domain = $1"
        )
        .bind(&request.domain)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        if existing_tenant > 0 {
            return Err(AppError::BadRequest(
                format!("Domain '{}' is already in use", request.domain)
            ));
        }

        let settings = request.settings.unwrap_or_default();
        let settings_json = settings.to_json();

        let tenant = sqlx::query_as::<_, Tenant>(
            r#"
            INSERT INTO tenants (
                name, domain, settings, subscription_tier, max_users, created_by
            ) VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id, name, domain, settings, is_active, subscription_tier, 
                     max_users, created_at, updated_at, created_by, updated_by
            "#,
        )
        .bind(&request.name)
        .bind(&request.domain)
        .bind(&settings_json)
        .bind(request.subscription_tier.unwrap_or_else(|| "basic".to_string()))
        .bind(request.max_users.unwrap_or(100))
        .bind(created_by)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        info!(
            tenant_id = %tenant.id,
            tenant_name = %tenant.name,
            domain = %tenant.domain,
            "Tenant created successfully"
        );

        Ok(TenantResponse::from(tenant))
    }

    #[instrument(skip(self))]
    pub async fn get_tenant(&self, tenant_id: Uuid) -> Result<TenantResponse, AppError> {
        let tenant = sqlx::query_as::<_, Tenant>(
            r#"
            SELECT id, name, domain, settings, is_active, subscription_tier,
                   max_users, created_at, updated_at, created_by, updated_by
            FROM tenants 
            WHERE id = $1
            "#,
        )
        .bind(tenant_id)
        .fetch_optional(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        match tenant {
            Some(tenant) => {
                let mut response = TenantResponse::from(tenant);
                
                // Get user and client counts
                let stats = self.get_tenant_stats(tenant_id).await?;
                response.user_count = Some(stats.user_count.unwrap_or(0) as i32);
                response.client_count = Some(stats.client_count.unwrap_or(0) as i32);

                Ok(response)
            }
            None => Err(AppError::NotFound("Tenant not found".to_string())),
        }
    }

    #[instrument(skip(self))]
    pub async fn get_tenant_by_domain(&self, domain: &str) -> Result<TenantResponse, AppError> {
        let tenant = sqlx::query_as::<_, Tenant>(
            r#"
            SELECT id, name, domain, settings, is_active, subscription_tier,
                   max_users, created_at, updated_at, created_by, updated_by
            FROM tenants 
            WHERE domain = $1 AND is_active = true
            "#,
        )
        .bind(domain)
        .fetch_optional(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        match tenant {
            Some(tenant) => Ok(TenantResponse::from(tenant)),
            None => Err(AppError::NotFound("Tenant not found".to_string())),
        }
    }

    #[instrument(skip(self))]
    pub async fn update_tenant(
        &self,
        tenant_id: Uuid,
        request: UpdateTenantRequest,
        updated_by: Option<Uuid>,
    ) -> Result<TenantResponse, AppError> {
        // Check if tenant exists
        let existing_tenant = self.get_tenant(tenant_id).await?;

        // Validate domain uniqueness if domain is being updated
        if let Some(ref new_domain) = request.domain {
            if new_domain != &existing_tenant.domain {
                let domain_count = sqlx::query_scalar::<_, i64>(
                    "SELECT COUNT(*) FROM tenants WHERE domain = $1 AND id != $2"
                )
                .bind(new_domain)
                .bind(tenant_id)
                .fetch_one(&self.db_pool)
                .await
                .map_err(|e| AppError::DatabaseError(e.to_string()))?;

                if domain_count > 0 {
                    return Err(AppError::BadRequest(
                        format!("Domain '{}' is already in use", new_domain)
                    ));
                }
            }
        }

        let settings_json = request.settings
            .map(|s| s.to_json())
            .unwrap_or_else(|| existing_tenant.settings.to_json());

        let tenant = sqlx::query_as::<_, Tenant>(
            r#"
            UPDATE tenants SET
                name = COALESCE($2, name),
                domain = COALESCE($3, domain),
                settings = COALESCE($4, settings),
                subscription_tier = COALESCE($5, subscription_tier),
                max_users = COALESCE($6, max_users),
                is_active = COALESCE($7, is_active),
                updated_at = NOW(),
                updated_by = $8
            WHERE id = $1
            RETURNING id, name, domain, settings, is_active, subscription_tier,
                     max_users, created_at, updated_at, created_by, updated_by
            "#,
        )
        .bind(tenant_id)
        .bind(request.name)
        .bind(request.domain)
        .bind(&settings_json)
        .bind(request.subscription_tier)
        .bind(request.max_users)
        .bind(request.is_active)
        .bind(updated_by)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        info!(
            tenant_id = %tenant.id,
            tenant_name = %tenant.name,
            "Tenant updated successfully"
        );

        Ok(TenantResponse::from(tenant))
    }

    #[instrument(skip(self))]
    pub async fn delete_tenant(&self, tenant_id: Uuid) -> Result<(), AppError> {
        // Check if tenant has active users or clients
        let user_count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM users WHERE tenant_id = $1 AND is_active = true"
        )
        .bind(tenant_id)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        let client_count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM service_clients WHERE tenant_id = $1 AND is_active = true"
        )
        .bind(tenant_id)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        if user_count > 0 || client_count > 0 {
            return Err(AppError::BadRequest(
                "Cannot delete tenant with active users or clients. Deactivate them first.".to_string()
            ));
        }

        let rows_affected = sqlx::query("DELETE FROM tenants WHERE id = $1")
            .bind(tenant_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| AppError::DatabaseError(e.to_string()))?
            .rows_affected();

        if rows_affected == 0 {
            return Err(AppError::NotFound("Tenant not found".to_string()));
        }

        info!(tenant_id = %tenant_id, "Tenant deleted successfully");
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_tenants(
        &self,
        request: TenantListRequest,
    ) -> Result<TenantListResponse, AppError> {
        let page = request.page.unwrap_or(1);
        let limit = request.limit.unwrap_or(20).min(100); // Max 100 per page
        let offset = (page - 1) * limit;

        let mut query = String::from(
            r#"
            SELECT id, name, domain, settings, is_active, subscription_tier,
                   max_users, created_at, updated_at, created_by, updated_by
            FROM tenants
            WHERE 1=1
            "#
        );

        let mut count_query = String::from("SELECT COUNT(*) FROM tenants WHERE 1=1");
        let mut bind_count = 0;

        // Add filters
        if let Some(ref search) = request.search {
            bind_count += 1;
            query.push_str(&format!(" AND (name ILIKE ${} OR domain ILIKE ${})", bind_count, bind_count));
            count_query.push_str(&format!(" AND (name ILIKE ${} OR domain ILIKE ${})", bind_count, bind_count));
        }

        if let Some(subscription_tier) = request.subscription_tier {
            bind_count += 1;
            query.push_str(&format!(" AND subscription_tier = ${}", bind_count));
            count_query.push_str(&format!(" AND subscription_tier = ${}", bind_count));
        }

        if let Some(is_active) = request.is_active {
            bind_count += 1;
            query.push_str(&format!(" AND is_active = ${}", bind_count));
            count_query.push_str(&format!(" AND is_active = ${}", bind_count));
        }

        query.push_str(" ORDER BY created_at DESC");
        query.push_str(&format!(" LIMIT ${} OFFSET ${}", bind_count + 1, bind_count + 2));

        // Execute count query
        let mut count_query_builder = sqlx::query_scalar::<_, i64>(&count_query);
        if let Some(ref search) = request.search {
            let search_pattern = format!("%{}%", search);
            count_query_builder = count_query_builder.bind(&search_pattern);
        }
        if let Some(ref subscription_tier) = request.subscription_tier {
            count_query_builder = count_query_builder.bind(subscription_tier);
        }
        if let Some(is_active) = request.is_active {
            count_query_builder = count_query_builder.bind(is_active);
        }

        let total = count_query_builder
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| AppError::DatabaseError(e.to_string()))? as u64;

        // Execute main query
        let mut query_builder = sqlx::query_as::<_, Tenant>(&query);
        if let Some(ref search) = request.search {
            let search_pattern = format!("%{}%", search);
            query_builder = query_builder.bind(&search_pattern);
        }
        if let Some(ref subscription_tier) = request.subscription_tier {
            query_builder = query_builder.bind(subscription_tier);
        }
        if let Some(is_active) = request.is_active {
            query_builder = query_builder.bind(is_active);
        }
        query_builder = query_builder.bind(limit as i64).bind(offset as i64);

        let tenants = query_builder
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        let tenant_responses: Vec<TenantResponse> = tenants
            .into_iter()
            .map(TenantResponse::from)
            .collect();

        let total_pages = (total + limit as u64 - 1) / limit as u64;

        Ok(TenantListResponse {
            tenants: tenant_responses,
            total,
            page,
            limit,
            total_pages: total_pages as u32,
        })
    }

    #[instrument(skip(self))]
    pub async fn get_tenant_stats(&self, tenant_id: Uuid) -> Result<TenantStats, AppError> {
        let stats = sqlx::query_as::<_, TenantStats>(
            r#"
            SELECT 
                t.id as tenant_id,
                t.name as tenant_name,
                t.domain,
                t.subscription_tier,
                COUNT(DISTINCT u.id) as user_count,
                COUNT(DISTINCT sc.id) as client_count,
                COUNT(DISTINCT us.id) as active_sessions,
                t.max_users,
                t.is_active as tenant_active,
                t.created_at as tenant_created_at
            FROM tenants t
            LEFT JOIN users u ON t.id = u.tenant_id AND u.is_active = true
            LEFT JOIN service_clients sc ON t.id = sc.tenant_id AND sc.is_active = true
            LEFT JOIN user_sessions us ON t.id = us.tenant_id AND us.is_active = true AND us.expires_at > NOW()
            WHERE t.id = $1
            GROUP BY t.id, t.name, t.domain, t.subscription_tier, t.max_users, t.is_active, t.created_at
            "#,
        )
        .bind(tenant_id)
        .fetch_optional(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        match stats {
            Some(stats) => Ok(stats),
            None => Err(AppError::NotFound("Tenant not found".to_string())),
        }
    }
}
