{{company_name}} - Multi-Factor Authentication Enabled

Hello {{username}},

SUCCESS! Multi-Factor Authentication (MFA) has been successfully enabled on your {{company_name}} account.

Your account is now more secure with an additional layer of protection. From now on, you'll need to provide both your password and a verification code from your authenticator app when signing in.

SECURITY TIPS:
- Keep your backup codes safe: Store them in a secure location separate from your authenticator app
- Don't share your codes: Never share your MFA codes with anyone
- Update your recovery info: Make sure your recovery email and phone number are current
- Use a trusted device: Only set up MFA on devices you own and trust

WHAT HAPPENS NEXT:
- Your next login will require both your password and MFA code
- You can manage your MFA settings in your account security section
- If you lose access to your authenticator app, use your backup codes

If you didn't enable MFA on your account, please contact our support team immediately as this may indicate unauthorized access to your account.

Need help? Contact us at {{support_email}}

© 2025 {{company_name}}. All rights reserved.
