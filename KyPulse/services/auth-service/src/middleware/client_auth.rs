use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpMessage, HttpResponse, Result,
};
use futures_util::future::LocalBoxFuture;
use std::{
    future::{ready, Ready},
    rc::Rc,
};
use uuid::Uuid;
use sqlx::PgPool;
use crate::models::{ServiceClient, ClientContext};
use crate::utils::errors::AppError;

pub struct ClientAuthMiddleware {
    pub db_pool: PgPool,
}

impl ClientAuthMiddleware {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }
}

impl<S, B> Transform<S, ServiceRequest> for ClientAuthMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = ClientAuthMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(ClientAuthMiddlewareService {
            service: Rc::new(service),
            db_pool: self.db_pool.clone(),
        }))
    }
}

pub struct ClientAuthMiddlewareService<S> {
    service: Rc<S>,
    db_pool: PgPool,
}

impl<S, B> Service<ServiceRequest> for ClientAuthMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = self.service.clone();
        let db_pool = self.db_pool.clone();

        Box::pin(async move {
            // Extract client_id from request
            let client_id = extract_client_id(&req)?;
            
            if let Some(client_id) = client_id {
                // Load client configuration from database
                match load_client_context(&db_pool, &client_id).await {
                    Ok(client_context) => {
                        // Validate request against client configuration
                        if let Err(e) = validate_client_request(&req, &client_context) {
                            return Ok(req.into_response(
                                HttpResponse::Forbidden()
                                    .json(serde_json::json!({
                                        "error": "client_validation_failed",
                                        "message": e.to_string()
                                    }))
                                    .into_body()
                            ));
                        }

                        // Set client context for downstream handlers
                        req.extensions_mut().insert(client_context);
                    }
                    Err(e) => {
                        return Ok(req.into_response(
                            HttpResponse::Unauthorized()
                                .json(serde_json::json!({
                                    "error": "invalid_client",
                                    "message": e.to_string()
                                }))
                                .into_body()
                        ));
                    }
                }
            }

            // Continue to next middleware/handler
            service.call(req).await
        })
    }
}

fn extract_client_id(req: &ServiceRequest) -> Result<Option<String>, Error> {
    // Try multiple methods to extract client_id
    
    // 1. From Authorization header (Bearer token with client_id)
    if let Some(auth_header) = req.headers().get("Authorization") {
        if let Ok(auth_str) = auth_header.to_str() {
            if auth_str.starts_with("Bearer ") {
                // For now, we'll extract client_id from query params or headers
                // In a full implementation, we'd decode the JWT token
            }
        }
    }

    // 2. From X-Client-ID header
    if let Some(client_id_header) = req.headers().get("X-Client-ID") {
        if let Ok(client_id) = client_id_header.to_str() {
            return Ok(Some(client_id.to_string()));
        }
    }

    // 3. From query parameter
    if let Some(client_id) = req.query_string().split('&')
        .find(|param| param.starts_with("client_id="))
        .and_then(|param| param.split('=').nth(1)) {
        return Ok(Some(client_id.to_string()));
    }

    // 4. From form data (for POST requests)
    // This would require reading the body, which is more complex in middleware
    // For now, we'll skip this and handle it in specific handlers

    Ok(None)
}

async fn load_client_context(
    db_pool: &PgPool,
    client_id: &str,
) -> Result<ClientContext, AppError> {
    let client = sqlx::query_as::<_, ServiceClient>(
        r#"
        SELECT id, client_id, client_secret_hash, client_name, client_type,
               tenant_id, allowed_origins, redirect_uris, oauth_providers, scopes,
               features, security_settings, is_active, is_development,
               rate_limit_requests_per_minute, rate_limit_requests_per_hour,
               created_at, updated_at, created_by, updated_by, last_used_at
        FROM service_clients 
        WHERE client_id = $1 AND is_active = true
        "#,
    )
    .bind(client_id)
    .fetch_optional(db_pool)
    .await
    .map_err(|e| AppError::DatabaseError(e.to_string()))?;

    match client {
        Some(client) => {
            // Update last_used_at timestamp
            let _ = sqlx::query(
                "UPDATE service_clients SET last_used_at = NOW() WHERE id = $1"
            )
            .bind(client.id)
            .execute(db_pool)
            .await;

            Ok(ClientContext::from(client))
        }
        None => Err(AppError::Unauthorized("Invalid client_id".to_string())),
    }
}

fn validate_client_request(
    req: &ServiceRequest,
    client_context: &ClientContext,
) -> Result<(), AppError> {
    // Validate origin
    if let Some(origin) = req.headers().get("Origin") {
        if let Ok(origin_str) = origin.to_str() {
            if !client_context.allowed_origins.contains(&origin_str.to_string()) 
                && !client_context.allowed_origins.contains(&"*".to_string()) {
                return Err(AppError::Forbidden(
                    format!("Origin '{}' not allowed for this client", origin_str)
                ));
            }
        }
    }

    // Validate HTTPS requirement
    if client_context.security_settings.require_https {
        let connection_info = req.connection_info();
        if connection_info.scheme() != "https" {
            return Err(AppError::Forbidden(
                "HTTPS is required for this client".to_string()
            ));
        }
    }

    // Additional validations can be added here:
    // - Rate limiting (would need Redis integration)
    // - IP whitelisting
    // - Time-based access restrictions
    // - Feature-specific validations

    Ok(())
}

// Helper function to get client context from request extensions
pub fn get_client_context(req: &ServiceRequest) -> Option<&ClientContext> {
    req.extensions().get::<ClientContext>()
}

// Helper function for handlers to get client context
pub fn extract_client_context(req: &actix_web::HttpRequest) -> Option<&ClientContext> {
    req.extensions().get::<ClientContext>()
}

// Tenant context middleware (sets tenant context based on client)
pub struct TenantContextMiddleware;

impl<S, B> Transform<S, ServiceRequest> for TenantContextMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = TenantContextMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(TenantContextMiddlewareService {
            service: Rc::new(service),
        }))
    }
}

pub struct TenantContextMiddlewareService<S> {
    service: Rc<S>,
}

impl<S, B> Service<ServiceRequest> for TenantContextMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = self.service.clone();

        Box::pin(async move {
            // Extract tenant_id from client context
            if let Some(client_context) = req.extensions().get::<ClientContext>() {
                let tenant_id = client_context.tenant_id;
                
                // Set tenant context in request extensions
                req.extensions_mut().insert(tenant_id);
                
                // Note: In a full implementation, we would also set the tenant context
                // in the database connection for RLS (Row-Level Security)
                // This would require custom database connection handling
            }

            service.call(req).await
        })
    }
}

// Helper to extract tenant ID from request
pub fn extract_tenant_id(req: &actix_web::HttpRequest) -> Option<Uuid> {
    req.extensions().get::<Uuid>().copied()
}
