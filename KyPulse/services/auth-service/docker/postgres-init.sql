-- PostgreSQL initialization script for KyPulse Auth Service
-- This script sets up the database for standalone deployment

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create auth service role for RLS
CREATE ROLE auth_service_role;

-- Grant necessary permissions to auth_service_role
GRANT CONNECT ON DATABASE kypulse_auth TO auth_service_role;
GRANT USAGE ON SCHEMA public TO auth_service_role;
GRANT CREATE ON SCHEMA public TO auth_service_role;

-- Grant permissions to the main auth_user
GRANT auth_service_role TO auth_user;

-- Create function to set tenant context (for RLS)
CREATE OR REPLACE FUNCTION set_tenant_context(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_tenant_id', tenant_uuid::text, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get current tenant context
CREATE OR REPLACE FUNCTION get_current_tenant_id()
RETURNS UUID AS $$
BEGIN
    RETURN current_setting('app.current_tenant_id', true)::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create function to clear tenant context
CREATE OR REPLACE FUNCTION clear_tenant_context()
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_tenant_id', '', true);
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions on tenant context functions
GRANT EXECUTE ON FUNCTION set_tenant_context(UUID) TO auth_service_role;
GRANT EXECUTE ON FUNCTION get_current_tenant_id() TO auth_service_role;
GRANT EXECUTE ON FUNCTION clear_tenant_context() TO auth_service_role;

-- Create a function to generate secure tokens
CREATE OR REPLACE FUNCTION generate_secure_token(length INTEGER DEFAULT 32)
RETURNS TEXT AS $$
BEGIN
    RETURN encode(gen_random_bytes(length), 'hex');
END;
$$ LANGUAGE plpgsql;

GRANT EXECUTE ON FUNCTION generate_secure_token(INTEGER) TO auth_service_role;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'KyPulse Auth Service database initialized successfully';
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE 'User: %', current_user;
    RAISE NOTICE 'Extensions enabled: uuid-ossp, pgcrypto';
    RAISE NOTICE 'Auth service role created and configured';
    RAISE NOTICE 'Tenant context functions created';
    RAISE NOTICE 'Ready for application migrations';
END $$;
