#!/bin/bash
set -e

# Standalone Auth Service Entrypoint Script
# This script handles initialization for standalone deployment

echo "🚀 Starting KyPulse Auth Service in Standalone Mode"
echo "=================================================="

# Function to wait for database
wait_for_db() {
    echo "⏳ Waiting for database connection..."
    
    # Extract database connection details from DATABASE_URL
    if [ -z "$DATABASE_URL" ]; then
        echo "❌ ERROR: DATABASE_URL environment variable is required"
        exit 1
    fi
    
    # Parse DATABASE_URL to get host and port
    DB_HOST=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
    DB_PORT=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
    
    if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ]; then
        echo "⚠️  Could not parse database host/port from DATABASE_URL"
        echo "   Attempting to connect anyway..."
    else
        echo "   Database Host: $DB_HOST"
        echo "   Database Port: $DB_PORT"
        
        # Wait for database to be ready
        for i in {1..30}; do
            if pg_isready -h "$DB_HOST" -p "$DB_PORT" -q; then
                echo "✅ Database is ready!"
                break
            fi
            echo "   Attempt $i/30: Database not ready, waiting 2 seconds..."
            sleep 2
        done
        
        if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -q; then
            echo "❌ ERROR: Database is not ready after 60 seconds"
            exit 1
        fi
    fi
}

# Function to run database migrations
run_migrations() {
    echo "🔄 Running database migrations..."
    
    # Check if sqlx-cli is available (it won't be in production)
    # In production, we'll use a simple SQL execution approach
    
    if [ -d "/app/migrations" ]; then
        echo "   Found migrations directory"
        
        # For now, we'll skip automatic migrations in production
        # This should be handled by a separate migration job or manual process
        echo "⚠️  Automatic migrations disabled in standalone mode"
        echo "   Please ensure database migrations are run separately"
        echo "   Migration files are available in /app/migrations"
    else
        echo "⚠️  No migrations directory found"
    fi
}

# Function to validate required environment variables
validate_env() {
    echo "🔍 Validating environment variables..."
    
    REQUIRED_VARS=(
        "DATABASE_URL"
        "REDIS_URL"
        "JWT_SECRET"
    )
    
    MISSING_VARS=()
    
    for var in "${REQUIRED_VARS[@]}"; do
        if [ -z "${!var}" ]; then
            MISSING_VARS+=("$var")
        fi
    done
    
    if [ ${#MISSING_VARS[@]} -ne 0 ]; then
        echo "❌ ERROR: Missing required environment variables:"
        for var in "${MISSING_VARS[@]}"; do
            echo "   - $var"
        done
        echo ""
        echo "Please set these environment variables and restart the service."
        exit 1
    fi
    
    echo "✅ All required environment variables are set"
}

# Function to wait for Redis
wait_for_redis() {
    echo "⏳ Waiting for Redis connection..."
    
    if [ -z "$REDIS_URL" ]; then
        echo "❌ ERROR: REDIS_URL environment variable is required"
        exit 1
    fi
    
    # Extract Redis host and port
    REDIS_HOST=$(echo $REDIS_URL | sed -n 's/redis:\/\/\([^:]*\):.*/\1/p')
    REDIS_PORT=$(echo $REDIS_URL | sed -n 's/redis:\/\/[^:]*:\([0-9]*\).*/\1/p')
    
    if [ -z "$REDIS_HOST" ]; then
        REDIS_HOST="localhost"
    fi
    if [ -z "$REDIS_PORT" ]; then
        REDIS_PORT="6379"
    fi
    
    echo "   Redis Host: $REDIS_HOST"
    echo "   Redis Port: $REDIS_PORT"
    
    # Wait for Redis to be ready
    for i in {1..30}; do
        if curl -f "http://$REDIS_HOST:$REDIS_PORT" >/dev/null 2>&1 || \
           timeout 2 bash -c "</dev/tcp/$REDIS_HOST/$REDIS_PORT" >/dev/null 2>&1; then
            echo "✅ Redis is ready!"
            break
        fi
        echo "   Attempt $i/30: Redis not ready, waiting 2 seconds..."
        sleep 2
    done
}

# Function to display configuration
show_config() {
    echo "📋 Service Configuration:"
    echo "   Service Mode: ${SERVICE_MODE:-integrated}"
    echo "   Host: ${HOST:-0.0.0.0}"
    echo "   Port: ${PORT:-8080}"
    echo "   Environment: ${ENVIRONMENT:-production}"
    echo "   Log Level: ${RUST_LOG:-info}"
    echo "   Database Max Connections: ${DATABASE_MAX_CONNECTIONS:-20}"
    echo "   Database Min Connections: ${DATABASE_MIN_CONNECTIONS:-5}"
    
    if [ "$SERVICE_MODE" = "standalone" ]; then
        echo "   Default Tenant: ${DEFAULT_TENANT_ID:-kypulse-main}"
    fi
    
    echo ""
}

# Function to create default tenant and client (if needed)
setup_default_data() {
    echo "🔧 Setting up default data..."
    
    # This would typically be done through the application's admin API
    # or a separate initialization script
    echo "   Default data setup will be handled by the application"
    echo "   on first startup if no tenants exist"
}

# Main execution
main() {
    echo "Starting initialization sequence..."
    echo ""
    
    # Validate environment
    validate_env
    echo ""
    
    # Show configuration
    show_config
    
    # Wait for dependencies
    wait_for_db
    echo ""
    
    wait_for_redis
    echo ""
    
    # Run migrations (if enabled)
    run_migrations
    echo ""
    
    # Setup default data
    setup_default_data
    echo ""
    
    echo "✅ Initialization complete!"
    echo "🚀 Starting Auth Service..."
    echo "=================================================="
    echo ""
    
    # Execute the main command
    exec "$@"
}

# Handle signals gracefully
trap 'echo "🛑 Received shutdown signal, stopping gracefully..."; exit 0' SIGTERM SIGINT

# Run main function
main "$@"
