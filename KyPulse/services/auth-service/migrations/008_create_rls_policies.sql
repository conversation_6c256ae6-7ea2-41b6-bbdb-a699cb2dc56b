-- Create Row-Level Security (RLS) policies for tenant isolation
-- Migration 008: Tenant Isolation with RLS

-- Enable RLS on tenant-aware tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_mfa_secrets ENABLE ROW LEVEL SECURITY;
ALTER TABLE trusted_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_clients ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for users table
CREATE POLICY users_tenant_isolation ON users
    FOR ALL
    TO auth_service_role
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

-- Create RLS policies for user_sessions table
CREATE POLICY user_sessions_tenant_isolation ON user_sessions
    FOR ALL
    TO auth_service_role
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

-- Create RLS policies for user_mfa_secrets table
CREATE POLICY user_mfa_secrets_tenant_isolation ON user_mfa_secrets
    FOR ALL
    TO auth_service_role
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

-- Create RLS policies for trusted_devices table
CREATE POLICY trusted_devices_tenant_isolation ON trusted_devices
    FOR ALL
    TO auth_service_role
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

-- Create RLS policies for service_clients table
CREATE POLICY service_clients_tenant_isolation ON service_clients
    FOR ALL
    TO auth_service_role
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

-- Create database role for the auth service
CREATE ROLE auth_service_role;

-- Grant necessary permissions to auth_service_role
GRANT CONNECT ON DATABASE postgres TO auth_service_role;
GRANT USAGE ON SCHEMA public TO auth_service_role;

-- Grant table permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO auth_service_role;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO auth_service_role;

-- Grant permissions on future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO auth_service_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO auth_service_role;

-- Create function to set tenant context
CREATE OR REPLACE FUNCTION set_tenant_context(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_tenant_id', tenant_uuid::text, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get current tenant context
CREATE OR REPLACE FUNCTION get_current_tenant_id()
RETURNS UUID AS $$
BEGIN
    RETURN current_setting('app.current_tenant_id', true)::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create function to clear tenant context
CREATE OR REPLACE FUNCTION clear_tenant_context()
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_tenant_id', '', true);
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions on tenant context functions
GRANT EXECUTE ON FUNCTION set_tenant_context(UUID) TO auth_service_role;
GRANT EXECUTE ON FUNCTION get_current_tenant_id() TO auth_service_role;
GRANT EXECUTE ON FUNCTION clear_tenant_context() TO auth_service_role;

-- Create admin bypass policies (for system operations)
CREATE POLICY users_admin_bypass ON users
    FOR ALL
    TO postgres
    USING (true);

CREATE POLICY user_sessions_admin_bypass ON user_sessions
    FOR ALL
    TO postgres
    USING (true);

CREATE POLICY user_mfa_secrets_admin_bypass ON user_mfa_secrets
    FOR ALL
    TO postgres
    USING (true);

CREATE POLICY trusted_devices_admin_bypass ON trusted_devices
    FOR ALL
    TO postgres
    USING (true);

CREATE POLICY service_clients_admin_bypass ON service_clients
    FOR ALL
    TO postgres
    USING (true);

-- Create indexes to support RLS performance
CREATE INDEX idx_users_tenant_id_active ON users(tenant_id, is_active);
CREATE INDEX idx_user_sessions_tenant_id_active ON user_sessions(tenant_id, is_active);
CREATE INDEX idx_service_clients_tenant_id_active ON service_clients(tenant_id, is_active);

-- Create view for tenant statistics (admin use)
CREATE VIEW tenant_stats AS
SELECT 
    t.id as tenant_id,
    t.name as tenant_name,
    t.domain,
    t.subscription_tier,
    COUNT(DISTINCT u.id) as user_count,
    COUNT(DISTINCT sc.id) as client_count,
    COUNT(DISTINCT us.id) as active_sessions,
    t.max_users,
    t.is_active as tenant_active,
    t.created_at as tenant_created_at
FROM tenants t
LEFT JOIN users u ON t.id = u.tenant_id AND u.is_active = true
LEFT JOIN service_clients sc ON t.id = sc.tenant_id AND sc.is_active = true
LEFT JOIN user_sessions us ON t.id = us.tenant_id AND us.is_active = true AND us.expires_at > NOW()
GROUP BY t.id, t.name, t.domain, t.subscription_tier, t.max_users, t.is_active, t.created_at;

-- Grant access to tenant stats view
GRANT SELECT ON tenant_stats TO auth_service_role;
