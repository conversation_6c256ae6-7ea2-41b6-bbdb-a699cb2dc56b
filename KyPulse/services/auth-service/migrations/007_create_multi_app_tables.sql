-- Create multi-app and tenant support tables
-- Migration 007: Multi-App Foundation

-- Create tenants table for multi-tenancy support
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE NOT NULL,
    settings JSONB NOT NULL DEFAULT '{}',
    
    -- Tenant status and configuration
    is_active BOOLEAN DEFAULT TRUE,
    subscription_tier VARCHAR(50) DEFAULT 'basic', -- basic, premium, enterprise
    max_users INTEGER DEFAULT 100,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES users(id) ON DELETE SET NULL
);

-- Create indexes for tenants
CREATE INDEX idx_tenants_domain ON tenants(domain);
CREATE INDEX idx_tenants_is_active ON tenants(is_active);
CREATE INDEX idx_tenants_created_at ON tenants(created_at);

-- Create service_clients table for multi-app support
CREATE TABLE service_clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id VARCHAR(255) UNIQUE NOT NULL,
    client_secret_hash VARCHAR(255) NOT NULL,
    client_name VARCHAR(255) NOT NULL,
    client_type VARCHAR(50) NOT NULL, -- 'web', 'mobile', 'service', 'api'
    
    -- Tenant association
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Client configuration
    allowed_origins TEXT[] DEFAULT '{}', -- Array of allowed origins
    redirect_uris TEXT[] DEFAULT '{}', -- Array of redirect URIs
    oauth_providers TEXT[] DEFAULT '{}', -- Enabled OAuth providers for this client
    scopes TEXT[] DEFAULT '{}', -- Available scopes for this client
    
    -- Client features and security
    features JSONB NOT NULL DEFAULT '{}', -- Client-specific feature flags
    security_settings JSONB NOT NULL DEFAULT '{}', -- Security configuration
    
    -- Client status
    is_active BOOLEAN DEFAULT TRUE,
    is_development BOOLEAN DEFAULT FALSE, -- Development vs production client
    
    -- Rate limiting and quotas
    rate_limit_requests_per_minute INTEGER DEFAULT 1000,
    rate_limit_requests_per_hour INTEGER DEFAULT 10000,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
    last_used_at TIMESTAMPTZ
);

-- Create indexes for service_clients
CREATE INDEX idx_service_clients_client_id ON service_clients(client_id);
CREATE INDEX idx_service_clients_tenant_id ON service_clients(tenant_id);
CREATE INDEX idx_service_clients_client_type ON service_clients(client_type);
CREATE INDEX idx_service_clients_is_active ON service_clients(is_active);
CREATE INDEX idx_service_clients_created_at ON service_clients(created_at);

-- Add tenant_id and client_id to existing tables
ALTER TABLE users ADD COLUMN tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE users ADD COLUMN client_id UUID REFERENCES service_clients(id) ON DELETE SET NULL;

ALTER TABLE user_sessions ADD COLUMN tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE user_sessions ADD COLUMN client_id UUID REFERENCES service_clients(id) ON DELETE SET NULL;

ALTER TABLE user_mfa_secrets ADD COLUMN tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE trusted_devices ADD COLUMN tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;

-- Add indexes for new foreign keys
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_client_id ON users(client_id);
CREATE INDEX idx_user_sessions_tenant_id ON user_sessions(tenant_id);
CREATE INDEX idx_user_sessions_client_id ON user_sessions(client_id);
CREATE INDEX idx_user_mfa_secrets_tenant_id ON user_mfa_secrets(tenant_id);
CREATE INDEX idx_trusted_devices_tenant_id ON trusted_devices(tenant_id);

-- Create client API keys table for service-to-service authentication
CREATE TABLE client_api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES service_clients(id) ON DELETE CASCADE,
    key_name VARCHAR(255) NOT NULL,
    api_key_hash VARCHAR(255) NOT NULL,
    api_key_prefix VARCHAR(20) NOT NULL, -- First few chars for identification
    
    -- Key permissions and scope
    scopes TEXT[] DEFAULT '{}',
    permissions JSONB DEFAULT '{}',
    
    -- Key lifecycle
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    last_used_at TIMESTAMPTZ,
    usage_count INTEGER DEFAULT 0,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    revoked_at TIMESTAMPTZ,
    revoked_by UUID REFERENCES users(id) ON DELETE SET NULL,
    revoked_reason VARCHAR(255)
);

-- Create indexes for client_api_keys
CREATE INDEX idx_client_api_keys_client_id ON client_api_keys(client_id);
CREATE INDEX idx_client_api_keys_api_key_prefix ON client_api_keys(api_key_prefix);
CREATE INDEX idx_client_api_keys_is_active ON client_api_keys(is_active);
CREATE INDEX idx_client_api_keys_expires_at ON client_api_keys(expires_at);

-- Insert default tenant for existing data
INSERT INTO tenants (id, name, domain, settings, subscription_tier, max_users) 
VALUES (
    gen_random_uuid(),
    'KyPulse Default',
    'kypulse.com',
    '{"oauth_providers": ["google", "github", "microsoft"], "mfa_required": false, "session_timeout": "24h"}',
    'enterprise',
    10000
);

-- Get the default tenant ID for use in default client creation
-- Note: This will be handled in the application code for proper UUID handling
