# Standalone Dockerfile for VPS deployment
# This builds the auth service as a completely independent service
FROM rust:1.75-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy Cargo files for dependency caching
COPY Cargo.toml Cargo.lock ./

# Create dummy main.rs to build dependencies
RUN mkdir -p src && \
    echo "fn main() {}" > src/main.rs

# Build dependencies (this layer will be cached)
RUN cargo build --release && \
    rm -rf src target/release/deps/auth_service*

# Copy source code and migrations
COPY src ./src
COPY migrations ./migrations

# Build the application
RUN cargo build --release

# Runtime stage - Debian slim for better compatibility
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libpq5 \
    libssl3 \
    curl \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Create app user for security
RUN useradd -m -u 1001 authservice

# Set working directory
WORKDIR /app

# Copy binary and migrations from builder stage
COPY --from=builder /app/target/release/auth-service /usr/local/bin/auth-service
COPY --from=builder /app/migrations ./migrations

# Create necessary directories and set permissions
RUN mkdir -p /app/logs /app/data && \
    chown -R authservice:authservice /app /usr/local/bin/auth-service && \
    chmod +x /usr/local/bin/auth-service

# Copy startup script
COPY docker/standalone-entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh && \
    chown authservice:authservice /usr/local/bin/entrypoint.sh

# Switch to app user
USER authservice

# Expose port
EXPOSE 8080

# Environment variables for standalone mode
ENV SERVICE_MODE=standalone
ENV RUST_LOG=info
ENV HOST=0.0.0.0
ENV PORT=8080
ENV DATABASE_MAX_CONNECTIONS=20
ENV DATABASE_MIN_CONNECTIONS=5

# Health check with longer timeout for standalone deployment
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=5 \
    CMD curl -f http://localhost:8080/health || exit 1

# Use entrypoint script for initialization
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["auth-service"]
