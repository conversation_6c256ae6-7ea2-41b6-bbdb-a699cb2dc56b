version: '3.8'

services:
  # PostgreSQL Database for Auth Service
  auth-postgres:
    image: postgres:15-alpine
    container_name: kypulse-auth-postgres
    environment:
      POSTGRES_DB: kypulse_auth
      POSTGRES_USER: auth_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-auth_secure_password_123}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - auth_postgres_data:/var/lib/postgresql/data
      - ./docker/postgres-init.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - auth-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U auth_user -d kypulse_auth"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis for Session Storage and Caching
  auth-redis:
    image: redis:7-alpine
    container_name: kypulse-auth-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_secure_password_123} --appendonly yes
    volumes:
      - auth_redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - auth-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  # KyPulse Auth Service
  auth-service:
    build:
      context: .
      dockerfile: Dockerfile.standalone
    container_name: kypulse-auth-service
    environment:
      # Service Configuration
      SERVICE_MODE: standalone
      HOST: 0.0.0.0
      PORT: 8080
      ENVIRONMENT: ${ENVIRONMENT:-production}
      RUST_LOG: ${LOG_LEVEL:-info}
      
      # Database Configuration
      DATABASE_URL: postgresql://auth_user:${POSTGRES_PASSWORD:-auth_secure_password_123}@auth-postgres:5432/kypulse_auth
      DATABASE_MAX_CONNECTIONS: 20
      DATABASE_MIN_CONNECTIONS: 5
      DATABASE_CONNECTION_TIMEOUT: 30
      
      # Redis Configuration
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis_secure_password_123}@auth-redis:6379
      REDIS_MAX_CONNECTIONS: 20
      REDIS_CONNECTION_TIMEOUT: 5000
      REDIS_COMMAND_TIMEOUT: 3000
      
      # Security Configuration
      JWT_SECRET: ${JWT_SECRET:-your_super_secure_jwt_secret_key_change_this_in_production}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-your_32_char_encryption_key_here}
      
      # Multi-App Configuration
      DEFAULT_TENANT_ID: kypulse-main
      
      # Email Configuration (Resend)
      EMAIL_PROVIDER: resend
      EMAIL_RESEND_API_KEY: ${RESEND_API_KEY:-}
      EMAIL_FROM_ADDRESS: ${EMAIL_FROM_ADDRESS:-<EMAIL>}
      EMAIL_FROM_NAME: ${EMAIL_FROM_NAME:-KyPulse Auth}
      EMAIL_REPLY_TO: ${EMAIL_REPLY_TO:-<EMAIL>}
      
      # OAuth Configuration (optional)
      OAUTH_ENABLED: ${OAUTH_ENABLED:-false}
      OAUTH_GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID:-}
      OAUTH_GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET:-}
      OAUTH_GITHUB_CLIENT_ID: ${GITHUB_CLIENT_ID:-}
      OAUTH_GITHUB_CLIENT_SECRET: ${GITHUB_CLIENT_SECRET:-}
      OAUTH_MICROSOFT_CLIENT_ID: ${MICROSOFT_CLIENT_ID:-}
      OAUTH_MICROSOFT_CLIENT_SECRET: ${MICROSOFT_CLIENT_SECRET:-}
      
      # Default Client Configuration
      KYPULSE_WEB_CLIENT_ID: ${KYPULSE_WEB_CLIENT_ID:-kypulse_web_client_123}
      KYPULSE_WEB_CLIENT_SECRET: ${KYPULSE_WEB_CLIENT_SECRET:-kypulse_web_secret_456}
      KYPULSE_WEB_CLIENT_NAME: "KyPulse Web Application"
      KYPULSE_WEB_TENANT_ID: kypulse-main
      KYPULSE_WEB_ALLOWED_ORIGINS: ${ALLOWED_ORIGINS:-https://app.kypulse.com,http://localhost:3000}
      KYPULSE_WEB_REDIRECT_URIS: ${REDIRECT_URIS:-https://app.kypulse.com/auth/callback,http://localhost:3000/auth/callback}
      KYPULSE_WEB_OAUTH_PROVIDERS: google,github,microsoft
      
      # Rate Limiting
      RATE_LIMITING_ENABLED: true
      RATE_LIMIT_REQUESTS_PER_MINUTE: 1000
      RATE_LIMIT_REQUESTS_PER_HOUR: 10000
      
      # Security Settings
      CORS_ORIGINS: ${CORS_ORIGINS:-https://app.kypulse.com,http://localhost:3000}
      REQUIRE_HTTPS: ${REQUIRE_HTTPS:-false}
      
    ports:
      - "${AUTH_SERVICE_PORT:-8080}:8080"
    networks:
      - auth-network
    depends_on:
      auth-postgres:
        condition: service_healthy
      auth-redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    volumes:
      - auth_logs:/app/logs
      - auth_data:/app/data

  # Nginx Reverse Proxy (optional, for SSL termination)
  auth-nginx:
    image: nginx:alpine
    container_name: kypulse-auth-nginx
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - auth-network
    depends_on:
      - auth-service
    restart: unless-stopped
    profiles:
      - nginx

networks:
  auth-network:
    driver: bridge
    name: kypulse-auth-network

volumes:
  auth_postgres_data:
    name: kypulse-auth-postgres-data
  auth_redis_data:
    name: kypulse-auth-redis-data
  auth_logs:
    name: kypulse-auth-logs
  auth_data:
    name: kypulse-auth-data
  nginx_logs:
    name: kypulse-auth-nginx-logs
