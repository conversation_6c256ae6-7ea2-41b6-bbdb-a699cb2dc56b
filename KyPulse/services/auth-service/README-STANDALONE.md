# KyPulse Auth Service - Standalone Deployment

This guide covers deploying the KyPulse Auth Service as a standalone, independent service on a VPS or dedicated server.

## 🎯 Overview

The standalone deployment allows the auth service to run completely independently with:
- ✅ Multi-tenant support with Row-Level Security (RLS)
- ✅ Multi-app client management
- ✅ Dedicated PostgreSQL database
- ✅ Redis for session storage and caching
- ✅ Docker containerization
- ✅ SSL/HTTPS support
- ✅ Automated deployment scripts
- ✅ Health monitoring and logging

## 📋 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM**: Minimum 2GB, Recommended 4GB+
- **Storage**: Minimum 20GB SSD
- **CPU**: 2+ cores recommended

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- curl
- openssl (for SSL certificates)

### Network Requirements
- Port 80 (HTTP)
- Port 443 (HTTPS)
- Port 8080 (Auth Service - can be internal)

## 🚀 Quick Start

### 1. <PERSON>lone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd KyPulse/services/auth-service

# Copy environment template
cp .env.standalone.example .env.standalone
```

### 2. Configure Environment

Edit `.env.standalone` with your settings:

```bash
# Required: Set secure passwords
POSTGRES_PASSWORD=your_secure_postgres_password
REDIS_PASSWORD=your_secure_redis_password

# Required: Set secure secrets (minimum 32 characters)
JWT_SECRET=your_super_secure_jwt_secret_key_minimum_32_characters
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Required: Email configuration
RESEND_API_KEY=re_your_resend_api_key_here
EMAIL_FROM_ADDRESS=<EMAIL>

# Required: Client configuration
KYPULSE_WEB_CLIENT_ID=your_web_client_id
KYPULSE_WEB_CLIENT_SECRET=your_web_client_secret
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
REDIRECT_URIS=https://yourdomain.com/auth/callback
```

### 3. Deploy

```bash
# Run the deployment script
./scripts/deploy-standalone.sh
```

The script will:
- ✅ Check prerequisites
- ✅ Validate configuration
- ✅ Build Docker images
- ✅ Start all services
- ✅ Wait for health checks
- ✅ Show deployment status

## 🔧 Manual Deployment

If you prefer manual deployment:

```bash
# Build and start services
docker-compose -f docker-compose.standalone.yml --env-file .env.standalone up -d

# Check status
docker-compose -f docker-compose.standalone.yml --env-file .env.standalone ps

# View logs
docker-compose -f docker-compose.standalone.yml --env-file .env.standalone logs -f
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │  Auth Service   │    │   PostgreSQL    │
│   (SSL Term.)   │◄──►│   (Rust/Actix)  │◄──►│   (Database)    │
│   Port 80/443   │    │   Port 8080     │    │   Port 5432     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │      Redis      │
                       │   (Sessions)    │
                       │   Port 6379     │
                       └─────────────────┘
```

## 🔐 Security Features

### Multi-Tenant Isolation
- Row-Level Security (RLS) policies
- Tenant-specific data isolation
- Client-based access control

### Authentication & Authorization
- JWT tokens with secure secrets
- Argon2 password hashing
- MFA support (TOTP, SMS)
- OAuth integration (Google, GitHub, Microsoft)

### Network Security
- HTTPS/SSL termination
- CORS protection
- Rate limiting
- Security headers

## 📊 Monitoring & Health Checks

### Health Endpoints
- `GET /health` - Overall service health
- `GET /api/v1/tenants/health` - Tenant service health
- `GET /api/v1/clients/health` - Client service health

### Monitoring
```bash
# Check service status
curl http://localhost:8080/health

# View logs
docker-compose logs -f auth-service

# Monitor resource usage
docker stats
```

## 🔄 Management Commands

### Service Management
```bash
# Stop services
./scripts/deploy-standalone.sh stop

# Restart services
./scripts/deploy-standalone.sh restart

# View logs
./scripts/deploy-standalone.sh logs

# Check status
./scripts/deploy-standalone.sh status
```

### Database Management
```bash
# Connect to database
docker-compose exec auth-postgres psql -U auth_user -d kypulse_auth

# Backup database
docker-compose exec auth-postgres pg_dump -U auth_user kypulse_auth > backup.sql

# Restore database
docker-compose exec -T auth-postgres psql -U auth_user -d kypulse_auth < backup.sql
```

## 🌐 API Endpoints

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Refresh token

### Multi-App Management
- `GET /api/v1/tenants` - List tenants
- `POST /api/v1/tenants` - Create tenant
- `GET /api/v1/clients` - List clients
- `POST /api/v1/clients` - Create client

### OAuth
- `GET /auth/oauth/{provider}` - Initiate OAuth flow
- `GET /auth/oauth/{provider}/callback` - OAuth callback

## 🔧 Configuration Reference

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `POSTGRES_PASSWORD` | ✅ | PostgreSQL password |
| `REDIS_PASSWORD` | ✅ | Redis password |
| `JWT_SECRET` | ✅ | JWT signing secret (32+ chars) |
| `ENCRYPTION_KEY` | ✅ | Encryption key (32 chars) |
| `RESEND_API_KEY` | ✅ | Resend email API key |
| `EMAIL_FROM_ADDRESS` | ✅ | From email address |
| `ALLOWED_ORIGINS` | ✅ | CORS allowed origins |
| `OAUTH_ENABLED` | ❌ | Enable OAuth (default: false) |

### Client Configuration
Each client application needs:
- Unique `client_id` and `client_secret`
- Allowed origins for CORS
- Redirect URIs for OAuth
- Feature flags and permissions

## 🚨 Troubleshooting

### Common Issues

**Service won't start:**
```bash
# Check logs
docker-compose logs auth-service

# Check environment
docker-compose config
```

**Database connection issues:**
```bash
# Check database health
docker-compose exec auth-postgres pg_isready -U auth_user

# Check connection from service
docker-compose exec auth-service curl http://localhost:8080/health
```

**SSL/HTTPS issues:**
```bash
# Generate self-signed certificates for testing
openssl req -x509 -newkey rsa:4096 -keyout docker/ssl/key.pem -out docker/ssl/cert.pem -days 365 -nodes
```

### Performance Tuning

**Database:**
- Adjust `DATABASE_MAX_CONNECTIONS` based on load
- Monitor connection pool usage
- Consider read replicas for high traffic

**Redis:**
- Adjust `REDIS_MAX_CONNECTIONS`
- Monitor memory usage
- Configure persistence settings

## 📈 Scaling

### Horizontal Scaling
- Deploy multiple auth service instances
- Use load balancer (nginx, HAProxy)
- Shared database and Redis

### Vertical Scaling
- Increase container resources
- Optimize database configuration
- Tune connection pools

## 🔄 Updates & Maintenance

### Updating the Service
```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker-compose build auth-service
docker-compose up -d auth-service
```

### Database Migrations
```bash
# Migrations are handled automatically on startup
# Check logs for migration status
docker-compose logs auth-service | grep migration
```

## 📞 Support

For issues and questions:
1. Check the logs: `docker-compose logs -f`
2. Verify configuration: `docker-compose config`
3. Test health endpoints: `curl http://localhost:8080/health`
4. Review this documentation
5. Check the main project documentation

## 🔒 Production Checklist

Before going to production:

- [ ] Set secure passwords for all services
- [ ] Configure proper SSL certificates
- [ ] Set up monitoring and alerting
- [ ] Configure automated backups
- [ ] Review and test disaster recovery
- [ ] Set up log aggregation
- [ ] Configure firewall rules
- [ ] Test all authentication flows
- [ ] Verify multi-tenant isolation
- [ ] Load test the service
