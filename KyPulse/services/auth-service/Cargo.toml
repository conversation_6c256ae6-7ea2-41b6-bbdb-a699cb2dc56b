[package]
name = "auth-service"
version = "0.1.0"
edition = "2021"
description = "KyPulse Authentication Service"
authors = ["KyPulse Team"]

[[bin]]
name = "auth-service"
path = "src/main.rs"

[lib]
name = "auth_service"
path = "src/lib.rs"

[dependencies]
# Workspace dependencies
actix-web.workspace = true
actix-cors.workspace = true
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
sqlx.workspace = true
uuid.workspace = true
chrono.workspace = true
anyhow.workspace = true
thiserror.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true
tracing-actix-web.workspace = true
config.workspace = true
dotenv.workspace = true
dotenvy = "0.15"

# Auth-specific dependencies
jsonwebtoken.workspace = true
argon2.workspace = true
validator.workspace = true
redis.workspace = true
reqwest.workspace = true
actix-web-httpauth.workspace = true
totp-rs = { version = "5.7.0", features = ["qr"] }
qrcode = { version = "0.14.1", features = ["image"] }
base32 = "0.5.1"
base64 = "0.22.1"
rand = { version = "0.8", features = ["std"] }
regex = "1.11.1"
futures-util = "0.3.30"

# OAuth dependencies
oauth2 = "4.4"
url = "2.4"

# Email dependencies
resend-rs = "0.7.0"

# Template engine for emails
handlebars = "4.4"

# Cryptographic dependencies
sha2 = "0.10"

[dev-dependencies]
actix-rt = "2.9"
actix-test = "0.1"
mockall = "0.12"
tokio-test = "0.4"

[lints]
workspace = true
