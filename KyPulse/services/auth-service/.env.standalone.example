# KyPulse Auth Service - Standalone Deployment Configuration
# Copy this file to .env.standalone and update the values for your deployment

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
ENVIRONMENT=production
LOG_LEVEL=info
AUTH_SERVICE_PORT=8080

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_PASSWORD=your_secure_postgres_password_here
POSTGRES_PORT=5432

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_PORT=6379

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# IMPORTANT: Change these in production!
JWT_SECRET=your_super_secure_jwt_secret_key_minimum_32_characters_long
ENCRYPTION_KEY=your_32_character_encryption_key

# =============================================================================
# EMAIL CONFIGURATION (RESEND)
# =============================================================================
RESEND_API_KEY=re_your_resend_api_key_here
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Your App Name
EMAIL_REPLY_TO=<EMAIL>

# =============================================================================
# OAUTH CONFIGURATION (OPTIONAL)
# =============================================================================
OAUTH_ENABLED=false

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# GitHub OAuth
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Microsoft OAuth
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret

# =============================================================================
# DEFAULT CLIENT CONFIGURATION
# =============================================================================
# These are the default client credentials for your application
KYPULSE_WEB_CLIENT_ID=your_web_client_id_here
KYPULSE_WEB_CLIENT_SECRET=your_web_client_secret_here

# Allowed origins for CORS (comma-separated)
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# OAuth redirect URIs (comma-separated)
REDIRECT_URIS=https://yourdomain.com/auth/callback,https://app.yourdomain.com/auth/callback

# CORS origins (comma-separated)
CORS_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# =============================================================================
# SSL/HTTPS CONFIGURATION
# =============================================================================
REQUIRE_HTTPS=true
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMITING_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=1000
RATE_LIMIT_REQUESTS_PER_HOUR=10000

# =============================================================================
# MONITORING AND HEALTH CHECKS
# =============================================================================
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# =============================================================================
# BACKUP CONFIGURATION (OPTIONAL)
# =============================================================================
# Set these if you want automated backups
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# =============================================================================
# DEVELOPMENT OVERRIDES
# =============================================================================
# Uncomment these for development/testing
# ENVIRONMENT=development
# LOG_LEVEL=debug
# REQUIRE_HTTPS=false
# OAUTH_ENABLED=true
